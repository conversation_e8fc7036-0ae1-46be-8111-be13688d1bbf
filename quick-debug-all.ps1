# Quick Debug Services - Selective Batch Launcher
# Directly executes Java debug commands for selected IM services

param(
    [string[]]$Services = @(),          # Specify services to start: im-tcp, im-service, im-message-store
    [switch]$All,                       # Start all services
    [switch]$Interactive,               # Interactive service selection
    [switch]$Help
)

if ($Help) {
    Write-Host "Quick Debug Services - Selective Batch Launcher" -ForegroundColor Cyan
    Write-Host "===============================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\quick-debug-all.ps1                           # Interactive service selection" -ForegroundColor White
    Write-Host "  .\quick-debug-all.ps1 -All                      # Start all services" -ForegroundColor White
    Write-Host "  .\quick-debug-all.ps1 im-service                # Start specific service" -ForegroundColor White
    Write-Host "  .\quick-debug-all.ps1 im-service im-tcp         # Start multiple services" -ForegroundColor White
    Write-Host "  .\quick-debug-all.ps1 -Interactive              # Force interactive mode" -ForegroundColor White
    Write-Host ""
    Write-Host "Available Services:" -ForegroundColor Yellow
    Write-Host "  im-tcp           - TCP Connection Service (Port 5005)" -ForegroundColor Blue
    Write-Host "  im-service       - Business Service (Port 5006)" -ForegroundColor Green
    Write-Host "  im-message-store - Message Store Service (Port 5007)" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Java Commands:" -ForegroundColor Yellow
    Write-Host "  java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml" -ForegroundColor Cyan
    Write-Host "  java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar" -ForegroundColor Cyan
    Write-Host "  java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5007 -jar im-message-store/target/im-message-store.jar" -ForegroundColor Cyan
    Write-Host ""
    exit 0
}

# Service definitions
$availableServices = @{
    "im-tcp" = @{
        name = "TCP Connection Service"
        jar = "im-tcp/target/im-tcp.jar"
        config = "im-tcp/src/main/resources/config-docker-cluster.yml"
        port = 5005
        color = "Blue"
    }
    "im-service" = @{
        name = "Business Service"
        jar = "im-service/target/im-service.jar"
        config = ""
        port = 5006
        color = "Green"
    }
    "im-message-store" = @{
        name = "Message Store Service"
        jar = "im-message-store/target/im-message-store.jar"
        config = ""
        port = 5007
        color = "Magenta"
    }
}

function Show-ServiceMenu {
    Write-Host ""
    Write-Host "Available Services:" -ForegroundColor Yellow
    Write-Host "==================" -ForegroundColor Cyan
    Write-Host "1. TCP Connection Service (im-tcp) - Debug Port: 5005" -ForegroundColor Blue
    Write-Host "2. Business Service (im-service) - Debug Port: 5006" -ForegroundColor Green
    Write-Host "3. Message Store Service (im-message-store) - Debug Port: 5007" -ForegroundColor Magenta
    Write-Host "4. All Services" -ForegroundColor White
    Write-Host "0. Exit" -ForegroundColor Red
    Write-Host ""
}

function Get-UserSelection {
    $selectedServices = @()

    do {
        Show-ServiceMenu
        $choice = Read-Host "Select services (1-4, or 0 to exit, multiple choices separated by comma)"

        if ($choice -eq "0") {
            Write-Host "Exiting..." -ForegroundColor Yellow
            exit 0
        }

        $choices = $choice -split "," | ForEach-Object { $_.Trim() }

        foreach ($c in $choices) {
            switch ($c) {
                "1" {
                    if ($selectedServices -notcontains "im-tcp") {
                        $selectedServices += "im-tcp"
                        Write-Host "Added: TCP Connection Service" -ForegroundColor Blue
                    }
                }
                "2" {
                    if ($selectedServices -notcontains "im-service") {
                        $selectedServices += "im-service"
                        Write-Host "Added: Business Service" -ForegroundColor Green
                    }
                }
                "3" {
                    if ($selectedServices -notcontains "im-message-store") {
                        $selectedServices += "im-message-store"
                        Write-Host "Added: Message Store Service" -ForegroundColor Magenta
                    }
                }
                "4" {
                    $selectedServices = @("im-tcp", "im-service", "im-message-store")
                    Write-Host "Added: All Services" -ForegroundColor White
                    break
                }
                default {
                    Write-Host "Invalid choice: $c" -ForegroundColor Red
                }
            }
        }

        if ($selectedServices.Count -gt 0) {
            Write-Host ""
            Write-Host "Selected services: $($selectedServices -join ', ')" -ForegroundColor Yellow
            $confirm = Read-Host "Continue with these services? (y/n)"
            if ($confirm -eq "y" -or $confirm -eq "Y" -or $confirm -eq "") {
                break
            } else {
                $selectedServices = @()
            }
        }

    } while ($true)

    return $selectedServices
}

function Start-SelectedService {
    param(
        [string]$ServiceKey,
        [hashtable]$ServiceConfig
    )

    Write-Host "Starting $($ServiceConfig.name) (Debug Port: $($ServiceConfig.port))..." -ForegroundColor $ServiceConfig.color

    # Check JAR file
    if (-not (Test-Path $ServiceConfig.jar)) {
        Write-Host "   ❌ JAR not found: $($ServiceConfig.jar)" -ForegroundColor Red
        return $null
    }

    # Build Java command
    $javaArgs = @(
        "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=$($ServiceConfig.port)",
        "-jar",
        $ServiceConfig.jar
    )

    if ($ServiceConfig.config -and (Test-Path $ServiceConfig.config)) {
        $javaArgs += $ServiceConfig.config
    }

    Write-Host "   Command: java $($javaArgs -join ' ')" -ForegroundColor Cyan

    try {
        $process = Start-Process -FilePath "java" -ArgumentList $javaArgs -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 3

        if (-not $process.HasExited) {
            Write-Host "   ✅ $($ServiceConfig.name) started successfully (PID: $($process.Id))" -ForegroundColor Green
            return $process
        } else {
            Write-Host "   ❌ $($ServiceConfig.name) failed to start" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "   ❌ $($ServiceConfig.name) failed: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

Write-Host "Quick Debug Services - Selective Batch Launcher" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# Determine which services to start
$servicesToStart = @()

if ($All) {
    $servicesToStart = @("im-tcp", "im-service", "im-message-store")
    Write-Host "Mode: Start all services" -ForegroundColor White
} elseif ($Services.Count -gt 0) {
    $servicesToStart = $Services
    Write-Host "Mode: Start specified services: $($Services -join ', ')" -ForegroundColor White
} elseif ($Interactive -or ($Services.Count -eq 0 -and -not $All)) {
    Write-Host "Mode: Interactive service selection" -ForegroundColor White
    $servicesToStart = Get-UserSelection
} else {
    $servicesToStart = @("im-tcp", "im-service", "im-message-store")
    Write-Host "Mode: Start all services (default)" -ForegroundColor White
}

# Validate selected services
$validServices = @()
foreach ($service in $servicesToStart) {
    if ($availableServices.ContainsKey($service)) {
        $validServices += $service
    } else {
        Write-Host "Warning: Unknown service '$service' ignored" -ForegroundColor Yellow
    }
}

if ($validServices.Count -eq 0) {
    Write-Host "No valid services selected. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host "Services to start: $($validServices -join ', ')" -ForegroundColor White
Write-Host ""

# Check JAR files for selected services
Write-Host "Checking JAR files..." -ForegroundColor Yellow
$allJarsExist = $true
foreach ($serviceKey in $validServices) {
    $service = $availableServices[$serviceKey]
    if (-not (Test-Path $service.jar)) {
        Write-Host "❌ $($service.name) JAR not found: $($service.jar)" -ForegroundColor Red
        $allJarsExist = $false
    } else {
        Write-Host "✅ $($service.name) JAR found" -ForegroundColor Green
    }
}

if (-not $allJarsExist) {
    Write-Host ""
    Write-Host "Please run: .\build.ps1" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Starting selected debug services..." -ForegroundColor Yellow
Write-Host ""

# Start selected services
$results = @{}
$counter = 1

foreach ($serviceKey in $validServices) {
    $serviceConfig = $availableServices[$serviceKey]
    Write-Host "$counter. " -NoNewline -ForegroundColor White
    $process = Start-SelectedService -ServiceKey $serviceKey -ServiceConfig $serviceConfig
    $results[$serviceKey] = $process
    $counter++
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "        Startup Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check which services are running
Write-Host ""
$runningServices = @()
$runningCount = 0

foreach ($serviceKey in $validServices) {
    $serviceConfig = $availableServices[$serviceKey]
    $process = $results[$serviceKey]

    if ($process -and -not $process.HasExited) {
        Write-Host "✅ $($serviceConfig.name) - PID: $($process.Id), Debug Port: $($serviceConfig.port)" -ForegroundColor $serviceConfig.color
        $runningServices += $serviceKey
        $runningCount++
    } else {
        Write-Host "❌ $($serviceConfig.name) - Failed to start" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Summary: $runningCount of $($validServices.Count) services started successfully" -ForegroundColor $(if ($runningCount -eq $validServices.Count) { "Green" } else { "Yellow" })

if ($runningCount -gt 0) {
    Write-Host ""
    Write-Host "🎯 VS Code Debug Connection:" -ForegroundColor Yellow
    Write-Host "   1. Press F5 in VS Code" -ForegroundColor White
    Write-Host "   2. Select debug configuration:" -ForegroundColor White

    foreach ($serviceKey in $runningServices) {
        $serviceConfig = $availableServices[$serviceKey]
        $attachName = switch ($serviceKey) {
            "im-tcp" { "Attach to IM-TCP" }
            "im-service" { "Attach to IM-Service" }
            "im-message-store" { "Attach to IM-Message-Store" }
        }
        Write-Host "      - '$attachName' (port $($serviceConfig.port))" -ForegroundColor $serviceConfig.color
    }

    Write-Host ""
    Write-Host "🛑 Stop all services: .\stop-im-en.ps1 -Force" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🎉 Selected services are running in background. Ready for debugging!" -ForegroundColor Green
}

Write-Host "========================================" -ForegroundColor Cyan
