# IM系统调试环境快速启动脚本
# 启动所有服务的调试模式，支持IDE远程调试

param(
    [string[]]$Services = @(),          # 指定启动的服务
    [switch]$NoSuspend,                 # 不暂停等待调试器连接
    [switch]$Sequential,                # 顺序启动（默认并行）
    [switch]$Help                       # 显示帮助
)

if ($Help) {
    Write-Host "IM系统调试环境启动脚本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\start-debug.ps1                     # 启动所有服务（调试模式）" -ForegroundColor White
    Write-Host "  .\start-debug.ps1 im-tcp im-service   # 只启动指定服务" -ForegroundColor White
    Write-Host "  .\start-debug.ps1 -NoSuspend          # 启动但不等待调试器" -ForegroundColor White
    Write-Host "  .\start-debug.ps1 -Sequential         # 顺序启动服务" -ForegroundColor White
    Write-Host ""
    Write-Host "调试端口:" -ForegroundColor Yellow
    Write-Host "  im-tcp:           5005" -ForegroundColor White
    Write-Host "  im-service:       5006" -ForegroundColor White
    Write-Host "  im-message-store: 5007" -ForegroundColor White
    Write-Host ""
    Write-Host "IDE连接配置:" -ForegroundColor Yellow
    Write-Host "  Host: localhost" -ForegroundColor White
    Write-Host "  Transport: Socket" -ForegroundColor White
    Write-Host "  Debugger mode: Attach to remote JVM" -ForegroundColor White
    Write-Host ""
    exit 0
}

# 服务配置
$serviceConfig = @{
    "im-tcp" = @{
        "jar" = "im-tcp/target/im-tcp.jar"
        "config" = "im-tcp/src/main/resources/config-docker-cluster.yml"
        "debugPort" = 5005
        "displayName" = "TCP连接服务"
        "logFile" = "logs/im-tcp-debug.log"
    }
    "im-service" = @{
        "jar" = "im-service/target/im-service.jar"
        "config" = ""
        "debugPort" = 5006
        "displayName" = "核心业务服务"
        "logFile" = "logs/im-service-debug.log"
    }
    "im-message-store" = @{
        "jar" = "im-message-store/target/im-message-store.jar"
        "config" = ""
        "debugPort" = 5007
        "displayName" = "消息存储服务"
        "logFile" = "logs/im-message-store-debug.log"
    }
}

function Write-DebugLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "DEBUG" { "Cyan" }
        default { "White" }
    }
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

function Test-ServiceJar {
    param([string]$ServiceName)
    
    $config = $serviceConfig[$ServiceName]
    if (-not (Test-Path $config.jar)) {
        Write-DebugLog "$ServiceName JAR文件不存在: $($config.jar)" "ERROR"
        Write-DebugLog "请先运行构建脚本: .\build-simple.ps1" "WARN"
        return $false
    }
    return $true
}

function Test-DebugPort {
    param([int]$Port)
    
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-DebugLog "端口 $Port 已被占用" "WARN"
            return $false
        }
        return $true
    }
    catch {
        return $true
    }
}

function Start-DebugService {
    param(
        [string]$ServiceName,
        [bool]$SuspendMode = $true
    )
    
    $config = $serviceConfig[$ServiceName]
    
    Write-DebugLog "启动 $($config.displayName) (调试端口: $($config.debugPort))" "INFO"
    
    # 检查JAR文件
    if (-not (Test-ServiceJar $ServiceName)) {
        return $null
    }
    
    # 检查调试端口
    if (-not (Test-DebugPort $config.debugPort)) {
        Write-DebugLog "$ServiceName 调试端口 $($config.debugPort) 被占用，跳过启动" "WARN"
        return $null
    }
    
    # 构建Java调试命令
    $javaArgs = @()
    
    # 调试参数
    $suspendFlag = if ($SuspendMode) { "y" } else { "n" }
    $javaArgs += "-agentlib:jdwp=transport=dt_socket,server=y,suspend=$suspendFlag,address=$($config.debugPort)"
    
    # JAR文件
    $javaArgs += "-jar"
    $javaArgs += $config.jar
    
    # 配置文件（如果有）
    if ($config.config -and (Test-Path $config.config)) {
        $javaArgs += $config.config
    }
    
    # 创建日志目录
    $logDir = Split-Path $config.logFile -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    try {
        Write-DebugLog "执行命令: java $($javaArgs -join ' ')" "DEBUG"
        
        # 启动进程
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = "java"
        $processInfo.Arguments = $javaArgs -join " "
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.CreateNoWindow = $true
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processInfo
        
        # 输出重定向到日志文件
        $process.add_OutputDataReceived({
            param($sender, $e)
            if ($e.Data) {
                Add-Content -Path $config.logFile -Value "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [OUT] $($e.Data)"
            }
        })
        
        $process.add_ErrorDataReceived({
            param($sender, $e)
            if ($e.Data) {
                Add-Content -Path $config.logFile -Value "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [ERR] $($e.Data)"
            }
        })
        
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        Write-DebugLog "$($config.displayName) 启动成功，PID: $($process.Id)" "SUCCESS"
        Write-DebugLog "调试端口: $($config.debugPort)" "SUCCESS"
        Write-DebugLog "日志文件: $($config.logFile)" "INFO"
        
        if ($SuspendMode) {
            Write-DebugLog "$($config.displayName) 等待调试器连接..." "WARN"
        }
        
        return $process
    }
    catch {
        Write-DebugLog "$($config.displayName) 启动失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Stop-AllDebugServices {
    Write-DebugLog "正在停止所有调试服务..." "INFO"
    
    # 使用现有的停止脚本
    if (Test-Path "stop-im-en.ps1") {
        & .\stop-im-en.ps1 -Force
    } else {
        Write-DebugLog "未找到停止脚本，请手动停止服务" "WARN"
    }
}

function Show-DebugInfo {
    param([hashtable]$RunningServices)
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "        调试环境启动完成" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    foreach ($serviceName in $RunningServices.Keys) {
        $config = $serviceConfig[$serviceName]
        $process = $RunningServices[$serviceName]
        
        if ($process -and -not $process.HasExited) {
            Write-Host ""
            Write-Host "$($config.displayName):" -ForegroundColor Yellow
            Write-Host "  PID: $($process.Id)" -ForegroundColor White
            Write-Host "  调试端口: $($config.debugPort)" -ForegroundColor Green
            Write-Host "  日志文件: $($config.logFile)" -ForegroundColor White
            Write-Host "  IDE连接: localhost:$($config.debugPort)" -ForegroundColor Cyan
        }
    }
    
    Write-Host ""
    Write-Host "IDE调试配置:" -ForegroundColor Yellow
    Write-Host "  1. 在IDE中创建Remote JVM Debug配置" -ForegroundColor White
    Write-Host "  2. 设置Host: localhost" -ForegroundColor White
    Write-Host "  3. 设置对应的端口号" -ForegroundColor White
    Write-Host "  4. 启动调试会话" -ForegroundColor White
    Write-Host ""
    Write-Host "按 Ctrl+C 停止所有服务" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
}

# 主执行逻辑
Write-Host "IM系统调试环境启动脚本" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# 确定要启动的服务
$servicesToStart = if ($Services.Count -gt 0) { $Services } else { $serviceConfig.Keys }

Write-DebugLog "准备启动服务: $($servicesToStart -join ', ')" "INFO"
Write-DebugLog "调试模式: $(if ($NoSuspend) { '不暂停' } else { '暂停等待调试器' })" "INFO"
Write-DebugLog "启动方式: $(if ($Sequential) { '顺序启动' } else { '并行启动' })" "INFO"

# 检查是否需要先停止现有服务
Write-DebugLog "检查现有服务..." "INFO"
$hasRunningServices = $false
foreach ($serviceName in $servicesToStart) {
    $config = $serviceConfig[$serviceName]
    if (-not (Test-DebugPort $config.debugPort)) {
        $hasRunningServices = $true
        break
    }
}

if ($hasRunningServices) {
    Write-DebugLog "检测到服务正在运行，是否先停止? (Y/N)" "WARN"
    $response = Read-Host
    if ($response -match "^[Yy]") {
        Stop-AllDebugServices
        Start-Sleep -Seconds 3
    }
}

# 启动服务
$runningServices = @{}
$suspendMode = -not $NoSuspend

if ($Sequential) {
    # 顺序启动
    foreach ($serviceName in $servicesToStart) {
        $process = Start-DebugService -ServiceName $serviceName -SuspendMode $suspendMode
        if ($process) {
            $runningServices[$serviceName] = $process
            Start-Sleep -Seconds 2
        }
    }
} else {
    # 并行启动
    foreach ($serviceName in $servicesToStart) {
        $process = Start-DebugService -ServiceName $serviceName -SuspendMode $suspendMode
        if ($process) {
            $runningServices[$serviceName] = $process
        }
    }
}

# 显示启动结果
if ($runningServices.Count -gt 0) {
    Show-DebugInfo -RunningServices $runningServices
    
    # 等待用户中断
    try {
        while ($true) {
            Start-Sleep -Seconds 1
            
            # 检查进程是否还在运行
            $aliveCount = 0
            foreach ($process in $runningServices.Values) {
                if ($process -and -not $process.HasExited) {
                    $aliveCount++
                }
            }
            
            if ($aliveCount -eq 0) {
                Write-DebugLog "所有服务已停止" "WARN"
                break
            }
        }
    }
    catch {
        Write-DebugLog "收到中断信号，正在停止服务..." "INFO"
        Stop-AllDebugServices
    }
} else {
    Write-DebugLog "没有服务成功启动" "ERROR"
    exit 1
}

Write-DebugLog "调试环境已停止" "INFO"
