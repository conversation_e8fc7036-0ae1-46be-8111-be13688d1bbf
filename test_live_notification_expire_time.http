### 测试直播提醒通知过期时间配置
### 测试环境：http://localhost:8000

### 1. 测试默认过期时间（不传递redisExpireMinutes参数）
POST http://localhost:8000/v1/system/notification/send
Content-Type: application/json
appId: 10000

{
    "notificationType": 5,
    "title": "直播开始提醒（默认过期时间）",
    "content": "您关注的主播正在直播，快来观看吧！",
    "receiverIds": ["user123", "user456"],
    "extra": {
        "roomId": "room123",
        "streamerName": "测试主播",
        "liveUrl": "https://live.example.com/room123"
    }
}

### 2. 测试自定义过期时间（60分钟）
POST http://localhost:8000/v1/system/notification/send
Content-Type: application/json
appId: 10000

{
    "notificationType": 5,
    "title": "直播开始提醒（60分钟过期）",
    "content": "您关注的主播正在直播，快来观看吧！",
    "redisExpireMinutes": 60,
    "receiverIds": ["user123", "user456"],
    "extra": {
        "roomId": "room456",
        "streamerName": "测试主播2",
        "liveUrl": "https://live.example.com/room456"
    }
}

### 3. 测试较长过期时间（12小时 = 720分钟）
POST http://localhost:8000/v1/system/notification/send
Content-Type: application/json
appId: 10000

{
    "notificationType": 5,
    "title": "重要直播预告（12小时过期）",
    "content": "明天晚上8点将有重要直播活动，敬请期待！",
    "redisExpireMinutes": 720,
    "receiverIds": ["user123", "user456", "user789"],
    "extra": {
        "roomId": "room789",
        "eventType": "special",
        "scheduledTime": "2025-07-10 20:00:00",
        "description": "年度盛典直播"
    }
}

### 4. 测试传递默认值43200（应该被重置为180分钟）
POST http://localhost:8000/v1/system/notification/send
Content-Type: application/json
appId: 10000

{
    "notificationType": 5,
    "title": "直播开始提醒（传递默认值）",
    "content": "您关注的主播正在直播，快来观看吧！",
    "redisExpireMinutes": 43200,
    "receiverIds": ["user123"],
    "extra": {
        "roomId": "room999",
        "streamerName": "测试主播3"
    }
}

### 5. 测试发送给普通用户（不包括游客）
POST http://localhost:8000/v1/system/notification/sendToNormalUsers
Content-Type: application/json
appId: 10000

{
    "notificationType": 5,
    "title": "全体用户直播提醒（自定义过期时间）",
    "content": "热门主播即将开播，不要错过！",
    "redisExpireMinutes": 90,
    "extra": {
        "roomId": "room888",
        "streamerName": "热门主播",
        "category": "游戏直播"
    }
}

### 6. 测试其他通知类型（验证不受影响）
POST http://localhost:8000/v1/system/notification/send
Content-Type: application/json
appId: 10000

{
    "notificationType": 1,
    "title": "系统维护通知",
    "content": "系统将于今晚进行维护",
    "redisExpireMinutes": 1440,
    "receiverIds": ["user123"]
}

### 验证查询接口

### 7. 查询用户未读通知
GET http://localhost:8000/v1/system/notification/unread?userId=user123&lastSequence=0&limit=20
appId: 10000

### 8. 查询用户所有通知
GET http://localhost:8000/v1/system/notification/all?userId=user123&pageNum=1&pageSize=20
appId: 10000

### 9. 查询按类型分组的最新通知
GET http://localhost:8000/v1/system/notification/latest-by-type?userId=user123
appId: 10000

### 10. 查询类型5的通知列表
GET http://localhost:8000/v1/system/notification/by-type?userId=user123&notificationType=5&pageNum=1&pageSize=10
appId: 10000
