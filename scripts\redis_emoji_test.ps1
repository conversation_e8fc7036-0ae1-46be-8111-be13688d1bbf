# =====================================================
# Redis Emoji编码测试脚本
# 专门用于验证Redis中emoji表情的存储和读取
# =====================================================

param(
    [string]$RedisHost = "127.0.0.1",
    [string]$RedisPort = "6379",
    [string]$RedisPassword = "",
    [string]$TestKey = "emoji_test_key"
)

Write-Host "=== Redis Emoji编码测试脚本 ===" -ForegroundColor Green
Write-Host "Redis地址: $RedisHost:$RedisPort" -ForegroundColor Yellow
Write-Host "测试键名: $TestKey" -ForegroundColor Yellow
Write-Host ""

# 测试用的emoji数据
$testData = @{
    "simple_emoji" = "Hello 😀"
    "complex_emoji" = "测试复杂表情 🎉🚀🌟✨"
    "mixed_content" = "用户消息：今天天气真好 ☀️，心情很棒 😊👍"
    "json_with_emoji" = '{"content":"直播间消息 🎊","user":"测试用户😀","type":1}'
}

# 函数：检查Redis连接
function Test-RedisConnection {
    Write-Host "1. 检查Redis连接..." -ForegroundColor Cyan
    
    try {
        $redisCmd = "redis-cli"
        if ($RedisPassword) {
            $redisCmd += " -a $RedisPassword"
        }
        $redisCmd += " -h $RedisHost -p $RedisPort ping"
        
        $result = Invoke-Expression $redisCmd 2>$null
        if ($result -eq "PONG") {
            Write-Host "✅ Redis连接成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Redis连接失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Redis连接测试异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：测试emoji存储
function Test-EmojiStorage {
    Write-Host "2. 测试emoji存储..." -ForegroundColor Cyan
    
    $successCount = 0
    $totalCount = $testData.Count
    
    foreach ($item in $testData.GetEnumerator()) {
        $key = "$TestKey:$($item.Key)"
        $value = $item.Value
        
        Write-Host "存储测试: $($item.Key)" -ForegroundColor Yellow
        Write-Host "  原始数据: $value" -ForegroundColor Gray
        
        try {
            $redisCmd = "redis-cli"
            if ($RedisPassword) {
                $redisCmd += " -a $RedisPassword"
            }
            $redisCmd += " -h $RedisHost -p $RedisPort --raw set `"$key`" `"$value`""
            
            $result = Invoke-Expression $redisCmd 2>$null
            if ($result -eq "OK") {
                Write-Host "  ✅ 存储成功" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "  ❌ 存储失败" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "  ❌ 存储异常: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "存储测试完成: $successCount/$totalCount 成功" -ForegroundColor Cyan
    return $successCount -eq $totalCount
}

# 函数：测试emoji读取
function Test-EmojiRetrieval {
    Write-Host "3. 测试emoji读取..." -ForegroundColor Cyan
    
    $successCount = 0
    $totalCount = $testData.Count
    
    foreach ($item in $testData.GetEnumerator()) {
        $key = "$TestKey:$($item.Key)"
        $originalValue = $item.Value
        
        Write-Host "读取测试: $($item.Key)" -ForegroundColor Yellow
        Write-Host "  原始数据: $originalValue" -ForegroundColor Gray
        
        try {
            $redisCmd = "redis-cli"
            if ($RedisPassword) {
                $redisCmd += " -a $RedisPassword"
            }
            $redisCmd += " -h $RedisHost -p $RedisPort --raw get `"$key`""
            
            $retrievedValue = Invoke-Expression $redisCmd 2>$null
            Write-Host "  读取数据: $retrievedValue" -ForegroundColor Gray
            
            if ($retrievedValue -eq $originalValue) {
                Write-Host "  ✅ 读取正确，emoji完整" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "  ❌ 读取错误，emoji可能损坏" -ForegroundColor Red
                Write-Host "    期望: $originalValue" -ForegroundColor Red
                Write-Host "    实际: $retrievedValue" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "  ❌ 读取异常: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "读取测试完成: $successCount/$totalCount 成功" -ForegroundColor Cyan
    return $successCount -eq $totalCount
}

# 函数：测试ZSet操作（模拟直播间消息存储）
function Test-ZSetEmojiOperations {
    Write-Host "4. 测试ZSet emoji操作（模拟直播间消息）..." -ForegroundColor Cyan
    
    $zsetKey = "$TestKey:messages"
    $testMessages = @(
        '{"content":"欢迎来到直播间 🎉","user":"主播😀","sequence":1}',
        '{"content":"今天直播内容很精彩 👍","user":"观众A😊","sequence":2}',
        '{"content":"送给主播一个礼物 🎁💖","user":"观众B🥰","sequence":3}'
    )
    
    # 清理旧数据
    try {
        $redisCmd = "redis-cli"
        if ($RedisPassword) {
            $redisCmd += " -a $RedisPassword"
        }
        $redisCmd += " -h $RedisHost -p $RedisPort del `"$zsetKey`""
        Invoke-Expression $redisCmd 2>$null | Out-Null
    }
    catch {
        # 忽略删除错误
    }
    
    # 添加消息到ZSet
    Write-Host "添加emoji消息到ZSet..." -ForegroundColor Yellow
    $addSuccess = 0
    for ($i = 0; $i -lt $testMessages.Count; $i++) {
        $message = $testMessages[$i]
        $score = $i + 1
        
        try {
            $redisCmd = "redis-cli"
            if ($RedisPassword) {
                $redisCmd += " -a $RedisPassword"
            }
            $redisCmd += " -h $RedisHost -p $RedisPort zadd `"$zsetKey`" $score `"$message`""
            
            $result = Invoke-Expression $redisCmd 2>$null
            if ($result -eq "1") {
                Write-Host "  ✅ 消息 $($i + 1) 添加成功" -ForegroundColor Green
                $addSuccess++
            } else {
                Write-Host "  ❌ 消息 $($i + 1) 添加失败" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "  ❌ 消息 $($i + 1) 添加异常: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 读取ZSet中的消息
    Write-Host "从ZSet读取emoji消息..." -ForegroundColor Yellow
    try {
        $redisCmd = "redis-cli"
        if ($RedisPassword) {
            $redisCmd += " -a $RedisPassword"
        }
        $redisCmd += " -h $RedisHost -p $RedisPort --raw zrange `"$zsetKey`" 0 -1"
        
        $retrievedMessages = Invoke-Expression $redisCmd 2>$null
        $readSuccess = 0
        
        if ($retrievedMessages) {
            foreach ($retrievedMsg in $retrievedMessages) {
                if ($retrievedMsg -and $retrievedMsg.Contains("😀") -or $retrievedMsg.Contains("🎉") -or $retrievedMsg.Contains("👍")) {
                    Write-Host "  ✅ 读取到完整emoji消息: $retrievedMsg" -ForegroundColor Green
                    $readSuccess++
                } else {
                    Write-Host "  ⚠️  读取到消息但emoji可能损坏: $retrievedMsg" -ForegroundColor Yellow
                }
            }
        }
        
        Write-Host "ZSet操作完成: 添加 $addSuccess/$($testMessages.Count)，读取 $readSuccess 条包含emoji的消息" -ForegroundColor Cyan
        return ($addSuccess -eq $testMessages.Count) -and ($readSuccess -gt 0)
    }
    catch {
        Write-Host "  ❌ ZSet读取异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 函数：清理测试数据
function Clear-TestData {
    Write-Host "5. 清理测试数据..." -ForegroundColor Cyan
    
    try {
        $redisCmd = "redis-cli"
        if ($RedisPassword) {
            $redisCmd += " -a $RedisPassword"
        }
        $redisCmd += " -h $RedisHost -p $RedisPort --raw keys `"$TestKey*`""
        
        $keys = Invoke-Expression $redisCmd 2>$null
        if ($keys) {
            foreach ($key in $keys) {
                if ($key) {
                    $delCmd = "redis-cli"
                    if ($RedisPassword) {
                        $delCmd += " -a $RedisPassword"
                    }
                    $delCmd += " -h $RedisHost -p $RedisPort del `"$key`""
                    Invoke-Expression $delCmd 2>$null | Out-Null
                }
            }
            Write-Host "✅ 测试数据清理完成" -ForegroundColor Green
        } else {
            Write-Host "ℹ️  没有找到需要清理的测试数据" -ForegroundColor Blue
        }
    }
    catch {
        Write-Host "⚠️  清理测试数据时出现异常: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 主执行流程
function Main {
    Write-Host "开始执行Redis emoji编码测试..." -ForegroundColor Green
    Write-Host ""
    
    $allSuccess = $true
    
    # 1. 检查Redis连接
    if (-not (Test-RedisConnection)) {
        Write-Host "❌ Redis连接失败，无法继续测试" -ForegroundColor Red
        return
    }
    Write-Host ""
    
    # 2. 测试emoji存储
    if (-not (Test-EmojiStorage)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 3. 测试emoji读取
    if (-not (Test-EmojiRetrieval)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 4. 测试ZSet操作
    if (-not (Test-ZSetEmojiOperations)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 5. 清理测试数据
    Clear-TestData
    Write-Host ""
    
    # 输出最终结果
    if ($allSuccess) {
        Write-Host "🎉 所有Redis emoji测试通过！编码配置正确！" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis emoji测试失败，请检查Redisson编码配置" -ForegroundColor Red
    }
}

# 执行主函数
Main
