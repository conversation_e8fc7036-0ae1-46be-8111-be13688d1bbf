# IM-Service 国际化功能测试脚本
# PowerShell 脚本，用于测试国际化功能

param(
    [string]$BaseUrl = "http://localhost:8000",
    [switch]$Verbose
)

Write-Host "=== IM-Service 国际化功能测试 ===" -ForegroundColor Green
Write-Host "测试服务地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

# 测试函数
function Test-I18nEndpoint {
    param(
        [string]$Url,
        [string]$Language,
        [string]$Method = "GET",
        [string]$Body = $null,
        [string]$Description
    )
    
    Write-Host "测试: $Description" -ForegroundColor Cyan
    Write-Host "语言: $Language" -ForegroundColor Gray
    Write-Host "请求: $Method $Url" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Accept-Language" = $Language
            "Content-Type" = "application/json"
        }
        
        if ($Method -eq "POST" -and $Body) {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $Body
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        Write-Host "响应成功:" -ForegroundColor Green
        $response | ConvertTo-Json -Depth 3 | Write-Host
        
        return $true
    }
    catch {
        Write-Host "响应失败:" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "错误详情: $responseBody" -ForegroundColor Red
        }
        return $false
    }
    finally {
        Write-Host ""
    }
}

# 检查服务是否启动
Write-Host "检查服务状态..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-RestMethod -Uri "$BaseUrl/v1/test/i18n/locale-info" -Method GET -TimeoutSec 5
    Write-Host "服务正常运行" -ForegroundColor Green
}
catch {
    Write-Host "服务未启动或无法访问，请先启动 im-service" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试用例
$testCases = @(
    @{
        Url = "$BaseUrl/v1/test/i18n/basic"
        Language = "zh-CN"
        Description = "基础消息 - 中文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/basic"
        Language = "en"
        Description = "基础消息 - 英文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/basic"
        Language = "vi-VI"
        Description = "基础消息 - 越南语"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/error-codes"
        Language = "zh-CN"
        Description = "错误码 - 中文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/error-codes"
        Language = "en"
        Description = "错误码 - 英文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/error-codes"
        Language = "vi-VI"
        Description = "错误码 - 越南语"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/validation"
        Language = "en"
        Method = "POST"
        Body = '{"userId":"","keyword":""}'
        Description = "参数验证 - 英文错误信息"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/validation"
        Language = "vi-VI"
        Method = "POST"
        Body = '{"userId":"","keyword":""}'
        Description = "参数验证 - 越南语错误信息"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/exception?type=user"
        Language = "en"
        Description = "异常处理 - 英文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/exception?type=user"
        Language = "vi-VI"
        Description = "异常处理 - 越南语"
    }
)

# 执行测试
$successCount = 0
$totalCount = $testCases.Count

foreach ($testCase in $testCases) {
    $result = Test-I18nEndpoint -Url $testCase.Url -Language $testCase.Language -Method $testCase.Method -Body $testCase.Body -Description $testCase.Description
    if ($result) {
        $successCount++
    }
}

# 测试结果汇总
Write-Host "=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host "总测试数: $totalCount" -ForegroundColor Yellow
Write-Host "成功数: $successCount" -ForegroundColor Green
Write-Host "失败数: $($totalCount - $successCount)" -ForegroundColor Red

if ($successCount -eq $totalCount) {
    Write-Host "所有测试通过！国际化功能正常工作。" -ForegroundColor Green
    exit 0
} else {
    Write-Host "部分测试失败，请检查服务配置和资源文件。" -ForegroundColor Red
    exit 1
}
