package com.lld.im.service.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Redis表情包存储测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestApplication.class)
@ActiveProfiles("test")
public class RedisEmojiTest {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testStringRedisTemplateEmoji() {
        String key = "test:emoji:string";
        String emojiText = "你好😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾";

        // 存储
        stringRedisTemplate.opsForValue().set(key, emojiText);

        // 读取
        String result = stringRedisTemplate.opsForValue().get(key);

        System.out.println("原始文本: " + emojiText);
        System.out.println("读取结果: " + result);
        System.out.println("是否相等: " + emojiText.equals(result));

        // 清理
        stringRedisTemplate.delete(key);
    }

    @Test
    public void testRedissonEmoji() {
        String key = "test:emoji:redisson";
        String emojiText = "你好😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩🥳😏😒😞😔😟😕🙁☹️😣😖😫😩🥺😢😭😤😠😡🤬🤯😳🥵🥶😱😨😰😥😓🤗🤔🤭🤫🤥😶😐😑😬🙄😯😦😧😮😲🥱😴🤤😪😵🤐🥴🤢🤮🤧😷🤒🤕🤑🤠😈👿👹👺🤡💩👻💀☠️👽👾🤖🎃😺😸😹😻😼😽🙀😿😾";

        RBucket<String> bucket = redissonClient.getBucket(key);

        // 存储
        bucket.set(emojiText);

        // 读取
        String result = bucket.get();

        System.out.println("原始文本: " + emojiText);
        System.out.println("读取结果: " + result);
        System.out.println("是否相等: " + emojiText.equals(result));

        // 清理
        bucket.delete();
    }

    @Test
    public void testJsonWithEmoji() {
        String key = "test:emoji:json";
        String emojiText = "你好😀😃😄😁😆😅😂🤣";

        // 模拟消息对象
        String jsonText = String.format("{\"content\":\"%s\",\"fromId\":\"test\",\"messageType\":1}", emojiText);

        // 存储JSON
        stringRedisTemplate.opsForValue().set(key, jsonText);

        // 读取JSON
        String result = stringRedisTemplate.opsForValue().get(key);

        System.out.println("原始JSON: " + jsonText);
        System.out.println("读取结果: " + result);
        System.out.println("是否相等: " + jsonText.equals(result));

        // 清理
        stringRedisTemplate.delete(key);
    }
}
