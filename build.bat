@echo off
title IM System Build Tool

echo ========================================
echo         IM System Build Tool
echo ========================================
echo.
echo Please select build option:
echo.
echo 1. Build All Modules
echo 2. Build Specific Module
echo 3. Clean Build All
echo 4. Quick Build (Skip Tests)
echo 5. Build and Package
echo 6. Exit
echo.

set /p choice=Please enter your choice (1-6): 

if "%choice%"=="1" (
    echo.
    echo Building all modules...
    powershell -ExecutionPolicy Bypass -File "%~dp0build-simple.ps1"
) else if "%choice%"=="2" (
    echo.
    echo Available modules: im-tcp, im-service, im-message-store
    set /p modules=Enter modules (space separated):
    echo Building modules: %modules%
    powershell -ExecutionPolicy Bypass -File "%~dp0build-simple.ps1" %modules%
) else if "%choice%"=="3" (
    echo.
    echo Clean building all modules...
    powershell -ExecutionPolicy Bypass -File "%~dp0build-simple.ps1" -Clean
) else if "%choice%"=="4" (
    echo.
    echo Quick building (skip tests)...
    powershell -ExecutionPolicy Bypass -File "%~dp0quick-build.ps1"
) else if "%choice%"=="5" (
    echo.
    echo Building and packaging...
    powershell -ExecutionPolicy Bypass -File "%~dp0build-simple.ps1" -Package
) else if "%choice%"=="6" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run again
    pause
    exit /b 1
)

echo.
echo Operation completed!
pause
