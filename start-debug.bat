@echo off
title IM System Debug Environment

echo ========================================
echo     IM System Debug Environment
echo ========================================
echo.
echo Please select debug option:
echo.
echo 1. Start All Services (Debug Mode)
echo 2. Start TCP Service Only (Port 5005)
echo 3. Start Business Service Only (Port 5006)
echo 4. Start Message Store Only (Port 5007)
echo 5. Start All (No Suspend)
echo 6. Stop All Services
echo 7. Exit
echo.

set /p choice=Please enter your choice (1-7): 

if "%choice%"=="1" (
    echo.
    echo Starting all services in debug mode...
    powershell -ExecutionPolicy Bypass -File "%~dp0start-debug.ps1"
) else if "%choice%"=="2" (
    echo.
    echo Starting TCP service in debug mode...
    powershell -ExecutionPolicy Bypass -File "%~dp0debug-quick.ps1" im-tcp
) else if "%choice%"=="3" (
    echo.
    echo Starting business service in debug mode...
    powershell -ExecutionPolicy Bypass -File "%~dp0debug-quick.ps1" im-service
) else if "%choice%"=="4" (
    echo.
    echo Starting message store in debug mode...
    powershell -ExecutionPolicy Bypass -File "%~dp0debug-quick.ps1" im-message-store
) else if "%choice%"=="5" (
    echo.
    echo Starting all services (no suspend)...
    powershell -ExecutionPolicy Bypass -File "%~dp0start-debug.ps1" -NoSuspend
) else if "%choice%"=="6" (
    echo.
    echo Stopping all services...
    powershell -ExecutionPolicy Bypass -File "%~dp0stop-im-en.ps1" -Force
) else if "%choice%"=="7" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run again
    pause
    exit /b 1
)

echo.
echo Operation completed!
pause
