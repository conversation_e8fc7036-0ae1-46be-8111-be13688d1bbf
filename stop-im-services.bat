@echo off
title IM Service Termination Tool

echo ========================================
echo     IM Service Termination Tool
echo ========================================
echo.
echo Please select an option:
echo.
echo 1. Simple version - Quick termination (Recommended)
echo 2. Full version - Feature rich
echo 3. Force termination - No confirmation
echo 4. Exit
echo.

set /p choice=Please enter your choice (1-4): 

if "%choice%"=="1" (
    echo.
    echo Running simple script...
    powershell -ExecutionPolicy Bypass -File "%~dp0stop-im-en.ps1"
) else if "%choice%"=="2" (
    echo.
    echo Running full script...
    powershell -ExecutionPolicy Bypass -File "%~dp0stop-im-services.ps1"
) else if "%choice%"=="3" (
    echo.
    echo Force terminating IM services...
    powershell -ExecutionPolicy Bypass -File "%~dp0stop-im-en.ps1" -Force
) else if "%choice%"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice, please run the program again
    pause
    exit /b 1
)

echo.
echo Operation completed!
pause
