# 直播间表情包消息乱码修复方案

## 问题描述

在直播间系统中，当发送包含表情包（emoji）的消息并存储到Redis缓存后，从Redis查询出来的消息内容出现乱码问题。

### 具体表现
1. 发送直播间消息时包含表情包字符（如😀、🎉等emoji）
2. 消息成功存储到Redis缓存中
3. 从Redis查询消息时，表情包字符显示为乱码或异常字符

## 问题分析

通过代码分析，发现了表情包消息乱码的关键问题主要集中在Redis相关的编码处理上：

### 1. Redisson编码器问题
- **问题**：所有Redis客户端策略都使用了`StringCodec`，但没有明确指定UTF-8编码
- **影响**：可能导致序列化/反序列化时的编码问题，emoji字符在Redis中存储时被错误编码

### 2. FastJSON序列化问题
- **问题**：在存储和读取Redis缓存时使用`JSONObject.toJSONString()`和`JSONObject.parseObject()`，没有确保正确处理UTF-8编码
- **影响**：JSON序列化过程中可能丢失或损坏emoji字符

**注意**：MySQL数据库本身已经支持emoji字符，`im_live_room_message`表使用utf8mb4字符集，数据库层面无需修改。

## 解决方案

### 1. 修复Redisson编码器配置

**修改文件**：
- `im-tcp/src/main/java/com/lld/im/tcp/redis/SingleClientStrategy.java`
- `im-tcp/src/main/java/com/lld/im/tcp/redis/ClusterClientStrategy.java`
- `im-tcp/src/main/java/com/lld/im/tcp/redis/SentinelClientStrategy.java`
- `im-service/src/main/java/com/lld/im/service/config/LazyRedisConfig.java`
- `im-message-store/src/main/java/com/lld/message/config/LazyRedisConfig.java`

**修改内容**：
```java
// 修改前
StringCodec stringCodec = new StringCodec();
config.setCodec(stringCodec);

// 修改后
// 设置编码器 - 明确指定UTF-8编码以支持emoji表情
StringCodec stringCodec = new StringCodec(java.nio.charset.StandardCharsets.UTF_8);
config.setCodec(stringCodec);
```

### 2. 创建安全的JSON序列化配置

**新增文件**：`im-service/src/main/java/com/lld/im/service/config/FastJsonConfig.java`

**功能**：
- 提供安全的JSON序列化/反序列化方法
- 确保emoji表情正确编码
- 配置FastJSON全局设置

### 3. 修改消息存储和查询逻辑

**修改文件**：
- `im-service/src/main/java/com/lld/im/service/liveroom/service/impl/LiveRoomServiceImpl.java`
- `im-tcp/src/main/java/com/lld/im/tcp/handler/LiveRoomMessageHandler.java`

**im-service模块修改内容**：
```java
// 修改前
redisZSetService.add(redisKey, JSONObject.toJSONString(msgResp), message.getSequence());
LiveRoomMsgResp msgResp = JSONObject.parseObject(messageJson, LiveRoomMsgResp.class);

// 修改后
redisZSetService.add(redisKey, FastJsonConfig.toJSONString(msgResp), message.getSequence());
LiveRoomMsgResp msgResp = FastJsonConfig.parseObject(messageJson, LiveRoomMsgResp.class);
```

**im-tcp模块修改内容**：
```java
// 修改前
sendToUser(appId, userId, standardMessage.toJSONString());
JSONObject msgObj = JSONObject.parseObject(cleanJson);

// 修改后
sendToUser(appId, userId, toSafeJSONString(standardMessage));
JSONObject msgObj = JSON.parseObject(cleanJson);
```

**关键改进**：
- 在`pushRecentMessagesToNewUser`方法中使用安全的JSON序列化
- 在`getRecentMessagesFromRedis`方法中使用安全的JSON反序列化
- 添加`toSafeJSONString`方法确保emoji表情正确编码
- 统一所有JSON处理方式，确保整个系统的emoji处理一致性

## 实施步骤

### 1. 代码修改
1. 按照上述方案修改所有相关配置文件
2. 部署新版本代码到测试环境

### 2. 服务重启
1. 重启所有相关服务（im-service、im-message-store、im-tcp）
2. 确保新的Redis编码配置生效

### 3. 功能验证
1. 发送包含emoji的直播间消息
2. 验证消息在Redis缓存中的存储和读取
3. 验证消息查询时emoji显示正常

## 验证方法

### 1. Redis缓存验证
```bash
# 连接Redis，查看缓存的消息内容
redis-cli
> ZRANGE liveroom:10000:test_room:messages 0 -1
```

### 2. 接口验证
- 调用直播间消息发送接口，发送包含emoji的消息
- 调用直播间消息查询接口，验证返回的消息内容

## 注意事项

1. **向后兼容性**：所有修改都保持向后兼容，不影响现有功能
2. **性能影响**：UTF-8编码配置对性能影响微乎其微
3. **部署简单**：只需重启服务，无需数据库操作
4. **监控告警**：部署后密切监控相关服务的运行状态

## 预期效果

修复完成后，直播间系统将能够：
1. 正确存储包含emoji的消息到Redis缓存
2. 正确从Redis缓存中读取包含emoji的消息
3. 在整个消息流程中保持emoji字符的完整性

**注意**：由于MySQL数据库本身已支持emoji，数据库持久化层面无需额外处理。
