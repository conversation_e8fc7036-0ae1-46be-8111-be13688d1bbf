# IM系统后台服务日志查看指南

## 🔍 问题说明

当使用 `.\quick-debug-all.ps1` 启动服务时，服务在后台运行（`WindowStyle Hidden`），无法直接看到日志输出。这是为了保持VS Code Terminal的整洁，但确实需要其他方式查看日志。

## 📋 查看后台服务日志的方法

### 方法1: 使用服务状态查看脚本 ⭐ 推荐

```powershell
# 查看运行中的服务状态
.\view-service-logs.ps1

# 查看详细信息
.\view-service-logs.ps1 -All

# 查看端口状态
.\view-service-logs.ps1 -Ports

# 查看详细服务信息
.\view-service-logs.ps1 -Status
```

**输出示例**：
```
Running IM Debug Services:
=========================

🟢 Business Service
   PID: 12345
   Debug Port: 5006
   Memory: 156.7 MB
   Uptime: 00:05:23

🟢 TCP Connection Service
   PID: 12346
   Debug Port: 5005
   Memory: 89.3 MB
   Uptime: 00:05:20

VS Code Debug Connection:
  - Attach to IM-Service (port 5006)
  - Attach to IM-TCP (port 5005)
```

### 方法2: 使用带日志输出的启动脚本 🔥 最佳体验

```powershell
# 启动单个服务并显示日志
.\debug-with-logs.ps1 im-service

# 交互式选择服务并显示日志
.\debug-with-logs.ps1

# 启动所有服务（会提供分别启动的建议）
.\debug-with-logs.ps1 -All
```

**特点**：
- ✅ 实时显示服务日志
- ✅ 在VS Code Terminal中运行
- ✅ 可以看到启动过程和错误信息
- ✅ 支持Ctrl+C停止服务

### 方法3: 在多个VS Code Terminal中分别启动

为了同时查看多个服务的日志：

1. **打开多个Terminal标签**：
   - 按 `Ctrl+Shift+\`` 创建新Terminal
   - 或点击Terminal面板的 `+` 按钮

2. **在每个Terminal中启动一个服务**：
   ```powershell
   # Terminal 1
   .\debug-with-logs.ps1 im-service
   
   # Terminal 2  
   .\debug-with-logs.ps1 im-tcp
   
   # Terminal 3
   .\debug-with-logs.ps1 im-message-store
   ```

3. **在Terminal标签间切换查看日志**

### 方法4: 使用VS Code任务

按 `Ctrl+Shift+P` → `Tasks: Run Task` → 选择：

- **Start Service with Logs (Interactive)** - 交互式选择并显示日志
- **View Service Status and Logs** - 查看当前服务状态

### 方法5: 手动检查进程和端口

```powershell
# 查看Java进程
Get-Process java | Select-Object Id, ProcessName, StartTime

# 查看调试端口占用情况
netstat -ano | findstr ":5005"
netstat -ano | findstr ":5006" 
netstat -ano | findstr ":5007"

# 查看进程详细信息
Get-Process -Id <PID> | Select-Object *
```

## 🎯 推荐的调试工作流

### 场景1: 开发调试（推荐）
```powershell
# 1. 启动单个服务并查看日志
.\debug-with-logs.ps1 im-service

# 2. 在VS Code中按F5连接调试器
# 3. 设置断点并调试
# 4. 在Terminal中查看实时日志
```

### 场景2: 多服务调试
```powershell
# 1. 打开3个VS Code Terminal标签
# 2. 分别启动服务：
#    Terminal 1: .\debug-with-logs.ps1 im-service
#    Terminal 2: .\debug-with-logs.ps1 im-tcp  
#    Terminal 3: .\debug-with-logs.ps1 im-message-store

# 3. 在VS Code中连接多个调试器
# 4. 在不同Terminal标签间切换查看日志
```

### 场景3: 快速后台启动 + 状态监控
```powershell
# 1. 快速启动所有服务（后台）
.\quick-debug-all.ps1 -All

# 2. 查看服务状态
.\view-service-logs.ps1 -All

# 3. 在VS Code中连接调试器
# 4. 需要时查看服务状态
.\view-service-logs.ps1
```

## 📊 日志输出对比

| 启动方式 | 日志可见性 | 启动速度 | 适用场景 |
|----------|------------|----------|----------|
| `quick-debug-all.ps1` | ❌ 后台运行 | ⚡ 快速 | 快速启动，专注调试 |
| `debug-with-logs.ps1` | ✅ 实时显示 | 🐌 较慢 | 需要查看日志，排查问题 |
| `debug-simple.ps1` | ❌ 后台运行 | ⚡ 快速 | 单服务快速启动 |

## 🔧 日志查看技巧

### 1. 实时监控服务状态
```powershell
# 创建一个监控循环
while ($true) {
    Clear-Host
    .\view-service-logs.ps1 -All
    Start-Sleep -Seconds 5
}
```

### 2. 查看特定端口的连接
```powershell
# 监控调试端口连接
netstat -ano | findstr ":5006" | findstr "ESTABLISHED"
```

### 3. 检查服务健康状态
```powershell
# 检查服务是否响应
Test-NetConnection -ComputerName localhost -Port 5006
```

## 🚨 故障排除

### 问题1: 服务启动失败但看不到错误
**解决方案**: 使用带日志的启动方式
```powershell
.\debug-with-logs.ps1 im-service
```

### 问题2: 不知道服务是否在运行
**解决方案**: 查看服务状态
```powershell
.\view-service-logs.ps1 -All
```

### 问题3: 调试器无法连接
**解决方案**: 检查端口状态
```powershell
.\view-service-logs.ps1 -Ports
```

### 问题4: 服务占用内存过高
**解决方案**: 查看详细状态
```powershell
.\view-service-logs.ps1 -Status
```

## 📝 最佳实践

### 开发阶段
- 使用 `debug-with-logs.ps1` 启动服务
- 在VS Code中开多个Terminal标签
- 实时查看日志输出

### 测试阶段  
- 使用 `quick-debug-all.ps1` 快速启动
- 定期使用 `view-service-logs.ps1` 检查状态
- 专注于功能测试

### 生产调试
- 使用后台启动方式
- 通过状态脚本监控服务
- 必要时切换到日志模式

## 🎉 总结

现在您有了完整的后台服务日志查看解决方案：

1. **✅ 服务状态查看** - `view-service-logs.ps1`
2. **✅ 实时日志显示** - `debug-with-logs.ps1`  
3. **✅ VS Code任务集成** - 一键查看状态
4. **✅ 多种工作流支持** - 适应不同场景

根据您的需求选择合适的方式：
- **需要看日志** → 使用 `debug-with-logs.ps1`
- **快速启动** → 使用 `quick-debug-all.ps1` + `view-service-logs.ps1`
- **状态监控** → 使用 `view-service-logs.ps1 -All`
