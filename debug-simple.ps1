# IM System Debug Launcher
param(
    [string]$Service = "",
    [switch]$NoSuspend,
    [switch]$Help
)

if ($Help) {
    Write-Host "IM System Debug Launcher" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\debug-simple.ps1                    # Start all services" -ForegroundColor White
    Write-Host "  .\debug-simple.ps1 im-tcp             # Start TCP service only" -ForegroundColor White
    Write-Host "  .\debug-simple.ps1 im-service         # Start business service only" -ForegroundColor White
    Write-Host "  .\debug-simple.ps1 im-message-store   # Start message store only" -ForegroundColor White
    Write-Host "  .\debug-simple.ps1 -NoSuspend         # Start without waiting for debugger" -ForegroundColor White
    Write-Host ""
    Write-Host "Debug Ports:" -ForegroundColor Yellow
    Write-Host "  im-tcp:           5005" -ForegroundColor White
    Write-Host "  im-service:       5006" -ForegroundColor White
    Write-Host "  im-message-store: 5007" -ForegroundColor White
    Write-Host ""
    exit 0
}

# Service configuration
$services = @{
    "im-tcp" = @{
        jar = "im-tcp/target/im-tcp.jar"
        config = "im-tcp/src/main/resources/config-docker-cluster.yml"
        port = 5005
        name = "TCP Service"
    }
    "im-service" = @{
        jar = "im-service/target/im-service.jar"
        config = ""
        port = 5006
        name = "Business Service"
    }
    "im-message-store" = @{
        jar = "im-message-store/target/im-message-store.jar"
        config = ""
        port = 5007
        name = "Message Store Service"
    }
}

function Start-ServiceDebug {
    param($ServiceName, $Config)
    
    Write-Host "Starting $($Config.name) (Debug Port: $($Config.port))" -ForegroundColor Yellow
    
    # Check JAR file
    if (-not (Test-Path $Config.jar)) {
        Write-Host "Error: JAR file not found - $($Config.jar)" -ForegroundColor Red
        Write-Host "Please run: .\build-simple.ps1" -ForegroundColor Yellow
        return
    }
    
    # Build command
    $suspend = if ($NoSuspend) { "n" } else { "y" }
    $debugArgs = "-agentlib:jdwp=transport=dt_socket,server=y,suspend=$suspend,address=$($Config.port)"
    
    $args = @($debugArgs, "-jar", $Config.jar)
    if ($Config.config -and (Test-Path $Config.config)) {
        $args += $Config.config
    }
    
    Write-Host "Command: java $($args -join ' ')" -ForegroundColor Cyan
    
    # Start service
    try {
        Start-Process -FilePath "java" -ArgumentList $args -PassThru
        Write-Host "Success: $($Config.name) started" -ForegroundColor Green
        Write-Host "  Debug Port: $($Config.port)" -ForegroundColor White
        if (-not $NoSuspend) {
            Write-Host "  Waiting for debugger connection..." -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "Failed: $($Config.name) start failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "IM System Debug Launcher" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

if ($Service) {
    # Start single service
    if ($services.ContainsKey($Service)) {
        Start-ServiceDebug $Service $services[$Service]
    } else {
        Write-Host "Error: Unknown service '$Service'" -ForegroundColor Red
        Write-Host "Available services: $($services.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
} else {
    # Start all services
    Write-Host "Starting all services..." -ForegroundColor White
    foreach ($serviceName in $services.Keys) {
        Start-ServiceDebug $serviceName $services[$serviceName]
        Start-Sleep -Seconds 1
    }
}

Write-Host ""
Write-Host "IDE Debug Configuration:" -ForegroundColor Cyan
Write-Host "1. Create Remote JVM Debug configuration" -ForegroundColor White
Write-Host "2. Host: localhost" -ForegroundColor White
Write-Host "3. Use corresponding port number" -ForegroundColor White
Write-Host ""
Write-Host "Use .\stop-im-en.ps1 to stop services" -ForegroundColor Yellow
