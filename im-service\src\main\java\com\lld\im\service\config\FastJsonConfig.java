package com.lld.im.service.config;

import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * FastJSON配置工具类
 * 提供安全的JSON序列化和反序列化方法，特别针对emoji表情处理
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class FastJsonConfig {

    /**
     * 专门用于处理emoji表情的JSON序列化
     * 确保emoji字符在序列化过程中不会被损坏
     *
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String toJSONStringWithEmoji(Object object) {
        if (object == null) {
            return null;
        }

        try {
            // 使用标准的JSON序列化，但确保字符串编码正确
            String jsonString = JSON.toJSONString(object);

            // 确保字符串使用UTF-8编码（处理emoji）
            byte[] utf8Bytes = jsonString.getBytes(StandardCharsets.UTF_8);
            return new String(utf8Bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("JSON序列化失败（emoji模式）: {}", object, e);
            // 降级到标准序列化
            return JSON.toJSONString(object);
        }
    }

    /**
     * 专门用于处理emoji表情的JSON反序列化
     * 确保emoji字符在反序列化过程中不会被损坏
     * 
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T parseObjectWithEmoji(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // 确保字符串使用UTF-8编码处理
            byte[] utf8Bytes = jsonString.getBytes(StandardCharsets.UTF_8);
            String utf8String = new String(utf8Bytes, StandardCharsets.UTF_8);

            return JSON.parseObject(utf8String, clazz);
        } catch (Exception e) {
            log.error("JSON反序列化失败（emoji模式）: {}", jsonString, e);
            // 降级到标准解析
            try {
                return JSON.parseObject(jsonString, clazz);
            } catch (Exception ex) {
                log.error("标准JSON反序列化也失败: {}", jsonString, ex);
                return null;
            }
        }
    }

    /**
     * 专门用于处理emoji表情的JSONObject解析
     * 
     * @param jsonString JSON字符串
     * @return JSONObject对象
     */
    public static JSONObject parseObjectWithEmoji(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // 确保字符串使用UTF-8编码
            if (jsonString.getBytes(StandardCharsets.UTF_8).length != jsonString.length()) {
                // 如果长度不一致，说明可能存在编码问题，重新编码
                jsonString = new String(jsonString.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            }

            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            log.error("JSONObject解析失败（emoji模式）: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 标准JSON序列化方法（保持兼容性）
     * 用于不涉及emoji的场景
     * 
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String toJSONString(Object object) {
        return JSON.toJSONString(object);
    }

    /**
     * 标准JSON反序列化方法（保持兼容性）
     * 用于不涉及emoji的场景
     * 
     * @param jsonString JSON字符串
     * @param clazz      目标类型
     * @param <T>        泛型类型
     * @return 反序列化后的对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        return JSON.parseObject(jsonString, clazz);
    }

    /**
     * 标准JSONObject解析方法（保持兼容性）
     * 用于不涉及emoji的场景
     * 
     * @param jsonString JSON字符串
     * @return JSONObject对象
     */
    public static JSONObject parseObject(String jsonString) {
        return JSON.parseObject(jsonString);
    }

    /**
     * 验证字符串是否包含emoji表情
     * 
     * @param text 要检查的文本
     * @return 是否包含emoji
     */
    public static boolean containsEmoji(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }

        // 检查是否包含emoji字符（基本的emoji范围检查）
        for (int i = 0; i < text.length(); i++) {
            int codePoint = text.codePointAt(i);

            // 检查常见的emoji Unicode范围
            if ((codePoint >= 0x1F600 && codePoint <= 0x1F64F) || // 表情符号
                    (codePoint >= 0x1F300 && codePoint <= 0x1F5FF) || // 杂项符号和象形文字
                    (codePoint >= 0x1F680 && codePoint <= 0x1F6FF) || // 交通和地图符号
                    (codePoint >= 0x1F1E0 && codePoint <= 0x1F1FF) || // 区域指示符号
                    (codePoint >= 0x2600 && codePoint <= 0x26FF) || // 杂项符号
                    (codePoint >= 0x2700 && codePoint <= 0x27BF)) { // 装饰符号
                return true;
            }

            // 如果是代理对，跳过下一个字符
            if (Character.isHighSurrogate((char) codePoint)) {
                i++;
            }
        }

        return false;
    }
}
