package com.lld.im.tcp.redis;


import com.lld.im.codec.config.BootstrapConfig;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;

/**
 * Redis单机模式连接策略
 *
 * @author: lld
 * @version: 1.0
 */
public class SingleClientStrategy implements RedisClientStrategy {

    @Override
    public RedissonClient getRedissonClient(BootstrapConfig.RedisConfig redisConfig) {
        Config config = new Config();
        String node = redisConfig.getSingle().getAddress();
        node = node.startsWith("redis://") ? node : "redis://" + node;
        SingleServerConfig serverConfig = config.useSingleServer()
                .setAddress(node)
                .setDatabase(redisConfig.getDatabase())
                .setTimeout(redisConfig.getTimeout())
                .setConnectionMinimumIdleSize(redisConfig.getPoolMinIdle())
                .setConnectTimeout(redisConfig.getPoolConnTimeout())
                .setConnectionPoolSize(redisConfig.getPoolSize());
        if (StringUtils.isNotBlank(redisConfig.getPassword())) {
            serverConfig.setPassword(redisConfig.getPassword());
        }
        // 设置编码器 - 明确指定UTF-8编码以支持emoji表情
        StringCodec stringCodec = new StringCodec(java.nio.charset.StandardCharsets.UTF_8);
        config.setCodec(stringCodec);
        return Redisson.create(config);
    }

    @Override
    public String getStrategyName() {
        return "single";
    }

}
