package com.lld.im.tcp.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * Emoji安全的JSON工具类
 * 专门用于处理包含emoji表情的JSON序列化和反序列化
 * 
 * <AUTHOR>
 */
@Slf4j
public class EmojiSafeJsonUtils {

    /**
     * 专门用于处理emoji表情的JSON序列化
     * 确保emoji字符在序列化过程中不会被损坏
     * 
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String toJSONStringWithEmoji(Object object) {
        if (object == null) {
            return null;
        }
        
        try {
            // 使用特定的序列化特性来确保emoji正确处理
            return JSON.toJSONString(object, 
                SerializerFeature.WriteMapNullValue,           // 输出null值
                SerializerFeature.WriteNullStringAsEmpty,      // 空字符串输出为""
                SerializerFeature.DisableCircularReferenceDetect, // 禁用循环引用检测
                SerializerFeature.WriteDateUseDateFormat      // 日期格式化
            );
        } catch (Exception e) {
            log.error("JSON序列化失败（emoji模式）: {}", object, e);
            // 降级到标准序列化
            return JSON.toJSONString(object);
        }
    }

    /**
     * 专门用于处理emoji表情的JSONObject解析
     * 
     * @param jsonString JSON字符串
     * @return JSONObject对象
     */
    public static JSONObject parseObjectWithEmoji(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 确保字符串使用UTF-8编码
            if (jsonString.getBytes(StandardCharsets.UTF_8).length != jsonString.length()) {
                // 如果长度不一致，说明可能存在编码问题，重新编码
                jsonString = new String(jsonString.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8);
            }
            
            return JSON.parseObject(jsonString);
        } catch (Exception e) {
            log.error("JSONObject解析失败（emoji模式）: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 验证字符串是否包含emoji表情
     * 
     * @param text 要检查的文本
     * @return 是否包含emoji
     */
    public static boolean containsEmoji(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        // 检查是否包含emoji字符（基本的emoji范围检查）
        for (int i = 0; i < text.length(); i++) {
            int codePoint = text.codePointAt(i);
            
            // 检查常见的emoji Unicode范围
            if ((codePoint >= 0x1F600 && codePoint <= 0x1F64F) ||  // 表情符号
                (codePoint >= 0x1F300 && codePoint <= 0x1F5FF) ||  // 杂项符号和象形文字
                (codePoint >= 0x1F680 && codePoint <= 0x1F6FF) ||  // 交通和地图符号
                (codePoint >= 0x1F1E0 && codePoint <= 0x1F1FF) ||  // 区域指示符号
                (codePoint >= 0x2600 && codePoint <= 0x26FF) ||    // 杂项符号
                (codePoint >= 0x2700 && codePoint <= 0x27BF)) {    // 装饰符号
                return true;
            }
            
            // 如果是代理对，跳过下一个字符
            if (Character.isHighSurrogate((char) codePoint)) {
                i++;
            }
        }
        
        return false;
    }
}
