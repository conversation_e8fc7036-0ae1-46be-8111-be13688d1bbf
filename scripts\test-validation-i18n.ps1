# IM-Service 参数验证国际化功能测试脚本
# PowerShell 脚本，用于测试参数验证的国际化功能

param(
    [string]$BaseUrl = "http://localhost:8000",
    [switch]$Verbose
)

Write-Host "=== IM-Service 参数验证国际化功能测试 ===" -ForegroundColor Green
Write-Host "测试服务地址: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

# 测试函数
function Test-ValidationEndpoint {
    param(
        [string]$Url,
        [string]$Language,
        [string]$Method = "POST",
        [string]$Body,
        [string]$Description
    )
    
    Write-Host "测试: $Description" -ForegroundColor Cyan
    Write-Host "语言: $Language" -ForegroundColor Gray
    Write-Host "请求: $Method $Url" -ForegroundColor Gray
    Write-Host "请求体: $Body" -ForegroundColor Gray
    
    try {
        $headers = @{
            "Accept-Language" = $Language
            "Content-Type" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers -Body $Body -ErrorAction Stop
        
        Write-Host "意外成功（应该返回验证错误）:" -ForegroundColor Yellow
        $response | ConvertTo-Json -Depth 3 | Write-Host
        
        return $false
    }
    catch {
        if ($_.Exception.Response.StatusCode -eq 400) {
            Write-Host "验证失败（预期）:" -ForegroundColor Green
            try {
                $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
                $responseBody = $reader.ReadToEnd()
                $errorResponse = $responseBody | ConvertFrom-Json
                Write-Host "错误信息: $($errorResponse.msg)" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "无法解析错误响应" -ForegroundColor Red
                return $false
            }
        }
        else {
            Write-Host "请求失败:" -ForegroundColor Red
            Write-Host $_.Exception.Message -ForegroundColor Red
            return $false
        }
    }
    finally {
        Write-Host ""
    }
}

# 检查服务是否启动
Write-Host "检查服务状态..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-RestMethod -Uri "$BaseUrl/v1/test/i18n/locale-info" -Method GET -TimeoutSec 5
    Write-Host "服务正常运行" -ForegroundColor Green
}
catch {
    Write-Host "服务未启动或无法访问，请先启动 im-service" -ForegroundColor Red
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 测试用例 - 参数验证国际化
$validationTestCases = @(
    @{
        Url = "$BaseUrl/v1/test/i18n/validation"
        Language = "zh-CN"
        Body = '{"userId":"","keyword":""}'
        Description = "参数验证错误 - 中文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/validation"
        Language = "en"
        Body = '{"userId":"","keyword":""}'
        Description = "参数验证错误 - 英文"
    },
    @{
        Url = "$BaseUrl/v1/test/i18n/validation"
        Language = "vi-VI"
        Body = '{"userId":"","keyword":""}'
        Description = "参数验证错误 - 越南语"
    },
    @{
        Url = "$BaseUrl/v1/group/updateGroup"
        Language = "en"
        Body = '{"groupId":"","groupName":"Test Group","appId":10000,"operatorId":"user123"}'
        Description = "群组更新验证 - 英文（群组ID为空）"
    },
    @{
        Url = "$BaseUrl/v1/group/updateGroup"
        Language = "vi-VI"
        Body = '{"groupId":"","groupName":"Test Group","appId":10000,"operatorId":"user123"}'
        Description = "群组更新验证 - 越南语（群组ID为空）"
    },
    @{
        Url = "$BaseUrl/v1/liveroom/createRoom"
        Language = "en"
        Body = '{"roomName":"","anchorId":"","appId":10000,"operatorId":"user123"}'
        Description = "创建直播间验证 - 英文（必填字段为空）"
    },
    @{
        Url = "$BaseUrl/v1/liveroom/createRoom"
        Language = "vi"
        Body = '{"roomName":"","anchorId":"","appId":10000,"operatorId":"user123"}'
        Description = "创建直播间验证 - 越南语（必填字段为空）"
    }
)

# 执行验证测试
$successCount = 0
$totalCount = $validationTestCases.Count

Write-Host "开始执行参数验证国际化测试..." -ForegroundColor Yellow
Write-Host ""

foreach ($testCase in $validationTestCases) {
    $result = Test-ValidationEndpoint -Url $testCase.Url -Language $testCase.Language -Body $testCase.Body -Description $testCase.Description
    if ($result) {
        $successCount++
    }
}

# 测试结果汇总
Write-Host "=== 参数验证国际化测试结果汇总 ===" -ForegroundColor Green
Write-Host "总测试数: $totalCount" -ForegroundColor Yellow
Write-Host "成功数: $successCount" -ForegroundColor Green
Write-Host "失败数: $($totalCount - $successCount)" -ForegroundColor Red

if ($successCount -eq $totalCount) {
    Write-Host "所有验证测试通过！参数验证国际化功能正常工作。" -ForegroundColor Green
    exit 0
} else {
    Write-Host "部分验证测试失败，请检查国际化配置和资源文件。" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "注意：此测试脚本验证参数验证错误消息的国际化功能。" -ForegroundColor Cyan
Write-Host "所有测试都应该返回400错误（参数验证失败），这是预期行为。" -ForegroundColor Cyan
Write-Host "重点是验证错误消息是否根据Accept-Language头正确显示不同语言。" -ForegroundColor Cyan
