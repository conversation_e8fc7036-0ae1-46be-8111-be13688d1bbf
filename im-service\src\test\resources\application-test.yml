# 测试环境配置
server:
  port: 8001

spring:
  profiles:
    active: test
  
  # 数据库配置（测试环境）
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************
    username: root
    password: root
    hikari:
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      maximum-pool-size: 10
      minimum-idle: 5

  # Redis配置（测试环境）
  redis:
    host: localhost
    port: 6379
    database: 1  # 使用数据库1避免与开发环境冲突
    password: 
    timeout: 3000
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1

  # RabbitMQ配置（测试环境）
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 应用配置
appConfig:
  privateKey: 123456
  zkAddr: 127.0.0.1:2181
  zkConnectTimeOut: 30000
  imRouteWay: 1
  consistentHashWay: 1
  callbackUrl: http://127.0.0.1:8989/callback
  modifyUserAfterCallback: true
  addFriendAfterCallback: true
  addFriendBeforeCallback: true
  deleteFriendAfterCallback: true
  modifyFriendAfterCallback: true
  createGroupAfterCallback: true
  updateGroupAfterCallback: true
  destroyGroupAfterCallback: true
  deleteGroupMemberAfterCallback: true
  addGroupMemberBeforeCallback: true
  addGroupMemberAfterCallback: true
  sendMessageAfterCallback: true
  sendMessageBeforeCallback: true

# 日志配置
logging:
  level:
    com.lld.im: DEBUG
    org.springframework: INFO
    root: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
