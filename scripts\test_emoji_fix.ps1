# =====================================================
# 直播间表情包消息修复验证脚本
# 专注于验证Redis缓存中emoji表情的正确处理
# =====================================================

param(
    [string]$BaseUrl = "http://localhost:8000",
    [string]$AppId = "10000",
    [string]$RoomId = "test_room_emoji",
    [string]$UserId = "test_user_emoji"
)

Write-Host "=== 直播间表情包消息修复验证脚本 ===" -ForegroundColor Green
Write-Host "基础URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "应用ID: $AppId" -ForegroundColor Yellow
Write-Host "直播间ID: $RoomId" -ForegroundColor Yellow
Write-Host "用户ID: $UserId" -ForegroundColor Yellow
Write-Host ""

# 测试用的emoji表情
$emojiMessages = @(
    "Hello World! 😀🎉",
    "测试中文emoji 👍💖",
    "复杂表情组合 🚀🌟✨🎊🎈",
    "各种表情 😂😍😎🤔😢😡🥰",
    "动物表情 🐶🐱🐭🐹🐰🦊🐻🐼",
    "食物表情 🍎🍌🍕🍔🍟🍰🎂🍭"
)

# 函数：发送HTTP请求
function Send-HttpRequest {
    param(
        [string]$Url,
        [string]$Method = "POST",
        [hashtable]$Headers = @{},
        [string]$Body = ""
    )
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $Headers -Body $Body -ContentType "application/json; charset=utf-8"
        return $response
    }
    catch {
        Write-Host "HTTP请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 函数：创建直播间
function Create-LiveRoom {
    Write-Host "1. 创建测试直播间..." -ForegroundColor Cyan
    
    $createRoomBody = @{
        appId = [int]$AppId
        roomId = $RoomId
        anchorId = $UserId
        roomName = "Emoji测试直播间 🎉"
        roomDesc = "用于测试表情包消息的直播间 😀"
        clientType = 1
        imei = "test_device_001"
    } | ConvertTo-Json -Depth 3
    
    $response = Send-HttpRequest -Url "$BaseUrl/liveroom/create" -Body $createRoomBody
    
    if ($response -and $response.isOk) {
        Write-Host "✅ 直播间创建成功" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ 直播间创建失败" -ForegroundColor Red
        return $false
    }
}

# 函数：加入直播间
function Join-LiveRoom {
    Write-Host "2. 加入直播间..." -ForegroundColor Cyan
    
    $joinRoomBody = @{
        appId = [int]$AppId
        roomId = $RoomId
        userId = $UserId
        nickname = "测试用户😀"
        avatar = "https://example.com/avatar.jpg"
        clientType = 1
        imei = "test_device_001"
    } | ConvertTo-Json -Depth 3
    
    $response = Send-HttpRequest -Url "$BaseUrl/liveroom/join" -Body $joinRoomBody
    
    if ($response -and $response.isOk) {
        Write-Host "✅ 加入直播间成功" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ 加入直播间失败" -ForegroundColor Red
        return $false
    }
}

# 函数：发送emoji消息
function Send-EmojiMessages {
    Write-Host "3. 发送包含emoji的测试消息..." -ForegroundColor Cyan
    
    $successCount = 0
    $totalCount = $emojiMessages.Count
    
    for ($i = 0; $i -lt $totalCount; $i++) {
        $message = $emojiMessages[$i]
        Write-Host "发送消息 $($i + 1)/$totalCount : $message" -ForegroundColor Yellow
        
        $sendMessageBody = @{
            appId = [int]$AppId
            roomId = $RoomId
            fromId = $UserId
            fromNickname = "测试用户😀"
            fromAvatar = "https://example.com/avatar.jpg"
            messageType = 1
            content = $message
            clientType = 1
            imei = "test_device_001"
        } | ConvertTo-Json -Depth 3
        
        $response = Send-HttpRequest -Url "$BaseUrl/liveroom/sendMessage" -Body $sendMessageBody
        
        if ($response -and $response.isOk) {
            Write-Host "  ✅ 消息发送成功" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "  ❌ 消息发送失败" -ForegroundColor Red
        }
        
        Start-Sleep -Milliseconds 500  # 避免发送过快
    }
    
    Write-Host "消息发送完成: $successCount/$totalCount 成功" -ForegroundColor Cyan
    return $successCount -eq $totalCount
}

# 函数：查询消息验证emoji
function Verify-EmojiMessages {
    Write-Host "4. 查询消息验证emoji显示..." -ForegroundColor Cyan
    
    $queryUrl = "$BaseUrl/liveroom/getRecentMessages?roomId=$RoomId&appId=$AppId&limit=20"
    $response = Send-HttpRequest -Url $queryUrl -Method "GET"
    
    if ($response -and $response.isOk -and $response.data) {
        Write-Host "✅ 消息查询成功，共 $($response.data.Count) 条消息" -ForegroundColor Green
        
        $emojiCount = 0
        foreach ($msg in $response.data) {
            if ($msg.content -match "[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]") {
                $emojiCount++
                Write-Host "  📱 包含emoji的消息: $($msg.content)" -ForegroundColor Yellow
            }
        }
        
        Write-Host "包含emoji的消息数量: $emojiCount" -ForegroundColor Cyan
        return $emojiCount -gt 0
    } else {
        Write-Host "❌ 消息查询失败" -ForegroundColor Red
        return $false
    }
}

# 函数：清理测试数据
function Cleanup-TestData {
    Write-Host "5. 清理测试数据..." -ForegroundColor Cyan
    
    # 这里可以添加清理逻辑，比如删除测试直播间等
    # 目前只是提示
    Write-Host "⚠️  请手动清理测试数据（如需要）" -ForegroundColor Yellow
}

# 主执行流程
function Main {
    Write-Host "开始执行emoji修复验证测试..." -ForegroundColor Green
    Write-Host ""
    
    $allSuccess = $true
    
    # 1. 创建直播间
    if (-not (Create-LiveRoom)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 2. 加入直播间
    if ($allSuccess -and -not (Join-LiveRoom)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 3. 发送emoji消息
    if ($allSuccess -and -not (Send-EmojiMessages)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 4. 验证emoji消息
    if ($allSuccess -and -not (Verify-EmojiMessages)) {
        $allSuccess = $false
    }
    Write-Host ""
    
    # 5. 清理测试数据
    Cleanup-TestData
    Write-Host ""
    
    # 输出最终结果
    if ($allSuccess) {
        Write-Host "🎉 所有测试通过！emoji表情修复成功！" -ForegroundColor Green
    } else {
        Write-Host "❌ 测试失败，请检查修复方案的实施情况" -ForegroundColor Red
    }
}

# 执行主函数
Main
