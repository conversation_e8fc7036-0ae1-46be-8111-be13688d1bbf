package com.lld.im.service.liveroom.service.impl;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.command.LiveRoomCommand;
import com.lld.im.service.config.FastJsonConfig;
import com.lld.im.service.liveroom.constant.LiveRoomErrorCode;
import com.lld.im.service.liveroom.dao.mapper.LiveRoomMapper;
import com.lld.im.service.liveroom.dao.mapper.LiveRoomMemberMapper;
import com.lld.im.service.liveroom.dao.mapper.LiveRoomMessageMapper;
import com.lld.im.service.liveroom.model.data.LiveRoomMessageData;
import com.lld.im.service.liveroom.model.entity.LiveRoom;
import com.lld.im.service.liveroom.model.entity.LiveRoomMember;
import com.lld.im.service.liveroom.model.entity.LiveRoomMessage;
import com.lld.im.service.liveroom.model.req.CheckLiveRoomExistsReq;
import com.lld.im.service.liveroom.model.req.CloseLiveRoomReq;
import com.lld.im.service.liveroom.model.req.CreateLiveRoomReq;
import com.lld.im.service.liveroom.model.req.JoinLiveRoomReq;
import com.lld.im.service.liveroom.model.req.KickUserReq;
import com.lld.im.service.liveroom.model.req.LeaveLiveRoomReq;
import com.lld.im.service.liveroom.model.req.MuteAllReq;
import com.lld.im.service.liveroom.model.req.MuteUserReq;
import com.lld.im.service.liveroom.model.req.SendLiveRoomMsgReq;
import com.lld.im.service.liveroom.model.req.UpdateAnnouncementReq;
import com.lld.im.service.liveroom.model.resp.LiveRoomExistsResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomMsgResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomUserResp;
import com.lld.im.service.liveroom.mq.LiveRoomMessageProducer;
import com.lld.im.service.liveroom.service.LiveRoomService;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.user.dao.ImUserDataEntity;
import com.lld.im.service.user.model.req.GetUserInfoReq;
import com.lld.im.service.user.model.resp.GetUserInfoResp;
import com.lld.im.service.user.service.ImUserService;
import com.lld.im.service.utils.MessageUtils;
import com.lld.im.service.utils.RedisZSetService;
import com.lld.im.service.utils.SnowflakeIdWorker;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 直播间服务实现类
 */
@Slf4j
@Service
public class LiveRoomServiceImpl implements LiveRoomService {

    @Autowired
    private LiveRoomMapper liveRoomMapper;

    @Autowired
    private LiveRoomMemberMapper liveRoomMemberMapper;

    @Autowired
    private LiveRoomMessageMapper liveRoomMessageMapper;

    @Autowired
    private ImUserService imUserService;

    @Autowired
    private RedisSeq redisSeq;

    @Autowired
    private RedisZSetService redisZSetService;

    @Autowired
    private LiveRoomMessageProducer liveRoomMessageProducer;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private MessageUtils messageUtils;

    /**
     * 判断用户是否为游客身份
     * 
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 是否为游客
     */
    private boolean isUserGuest(Integer appId, String userId) {
        if (appId == null || StringUtils.isEmpty(userId)) {
            return false;
        }

        // 从Redis中获取游客标识
        String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
        return Boolean.TRUE.equals(stringRedisTemplate.hasKey(guestKey));
    }

    /**
     * 创建直播间
     */
    @Override
    @Transactional
    public ResponseVO<String> createLiveRoom(CreateLiveRoomReq req) {
        // 如果直播间ID为空，则生成一个随机ID
        String roomId = req.getRoomId();
        if (StringUtils.isEmpty(roomId)) {
            roomId = IdUtil.simpleUUID();
        }

        // 创建直播间
        LiveRoom liveRoom = new LiveRoom();
        BeanUtil.copyProperties(req, liveRoom);
        liveRoom.setRoomId(roomId);
        liveRoom.setStatus(0); // 默认未开播
        liveRoom.setCreateTime(new Date());
        liveRoom.setUpdateTime(new Date());
        liveRoomMapper.insert(liveRoom);

        // 添加主播为直播间成员
        LiveRoomMember anchorMember = new LiveRoomMember();
        anchorMember.setRoomId(roomId);
        anchorMember.setUserId(req.getAnchorId());
        anchorMember.setRole(1); // 1-主播
        anchorMember.setMute(0); // 不禁言
        anchorMember.setAppId(req.getAppId());
        anchorMember.setJoinTime(new Date());
        anchorMember.setLastActiveTime(new Date());
        anchorMember.setCreateTime(new Date());
        anchorMember.setUpdateTime(new Date());
        liveRoomMemberMapper.insert(anchorMember);

        // 主播自动加入聊天室
        autoJoinAnchorToChatRoom(roomId, req.getAnchorId(), req.getAppId(),
                req.getClientType(), req.getImei());

        return ResponseVO.successResponse(roomId);
    }

    /**
     * 加入直播间
     *
     * @param req 加入直播间请求
     * @return 直播间信息
     */
    @Override
    public ResponseVO<LiveRoomResp> joinLiveRoom(JoinLiveRoomReq req) {
        if (req.getAppId() == null || StringUtils.isEmpty(req.getRoomId()) || StringUtils.isEmpty(req.getUserId())) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.PARAM_ERROR);
        }

        // 判断直播间是否存在
        LiveRoom liveRoom = liveRoomMapper.selectOne(
                Wrappers.<LiveRoom>lambdaQuery()
                        .eq(LiveRoom::getRoomId, req.getRoomId())
                        .eq(LiveRoom::getAppId, req.getAppId()));
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        // 判断用户是否为游客
        boolean isGuest = isUserGuest(req.getAppId(), req.getUserId());

        // 非游客时，判断用户是否存在
        if (!isGuest) {
            ResponseVO<ImUserDataEntity> userInfoResp = imUserService.getSingleUserInfo(req.getUserId(),
                    req.getAppId());
            if (userInfoResp == null || userInfoResp.getData() == null) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.USER_NOT_EXIST);
            }
        }

        // 优化：通过Redis判断用户是否已经在直播间（在线状态）
        // boolean isUserOnlineInRoom = isUserOnlineInRoom(req.getRoomId(),
        // req.getUserId(), isGuest);

        // 查询数据库记录（用于获取用户历史信息，仅针对正式用户）
        LiveRoomMember existMember = null;
        if (!isGuest) {
            existMember = liveRoomMemberMapper.selectOne(
                    Wrappers.<LiveRoomMember>lambdaQuery()
                            .eq(LiveRoomMember::getRoomId, req.getRoomId())
                            .eq(LiveRoomMember::getUserId, req.getUserId())
                            .eq(LiveRoomMember::getAppId, req.getAppId()));
        }

        // if (!isUserOnlineInRoom) {}

        // 对于正式用户，需要检查数据库记录是否存在（避免唯一约束冲突）
        if (!isGuest && existMember == null) {
            // 正式用户且数据库中没有记录，创建新记录
            LiveRoomMember member = new LiveRoomMember();
            member.setRoomId(req.getRoomId());
            member.setUserId(req.getUserId());
            member.setRole(req.getRole() != null ? req.getRole() : 4); // 默认为普通用户
            member.setMute(0); // 不禁言
            member.setAppId(req.getAppId());
            member.setJoinTime(new Date());
            member.setLastActiveTime(new Date());
            member.setCreateTime(new Date());
            member.setUpdateTime(new Date());
            member.setNickname(req.getNickname());
            member.setAvatar(req.getAvatar());
            liveRoomMemberMapper.insert(member);

            log.info("正式用户加入直播间，已存储到数据库: roomId={}, userId={}, nickname={}",
                    req.getRoomId(), req.getUserId(), req.getNickname());
        } else if (!isGuest && existMember != null) {
            // 正式用户且数据库中有记录，更新记录
            existMember.setLastActiveTime(new Date());
            existMember.setUpdateTime(new Date());
            // 更新昵称和头像
            existMember.setNickname(req.getNickname());
            existMember.setAvatar(req.getAvatar());
            liveRoomMemberMapper.updateById(existMember);

            log.info("正式用户重新加入直播间，已更新数据库记录: roomId={}, userId={}", req.getRoomId(), req.getUserId());
        }

        // 2. 发送结构化加入消息 (command=5012, action="join")
        // 用途：客户端更新在线用户列表和用户状态管理
        // 特点：触发SessionSocketHolder.addUserToRoom()操作，包含用户详细信息
        LiveRoomMessageData joinMessageData = LiveRoomMessageData.createJoinMessage(
                req.getRoomId(), req.getUserId(), req.getNickname(),
                req.getAvatar(), req.getRole(), isGuest);

        log.info("发送结构化加入消息: roomId={}, userId={}, nickname={}, command={}",
                req.getRoomId(), req.getUserId(), req.getNickname(), LiveRoomCommand.LIVE_ROOM_JOIN.getCommand());

        // 发送结构化加入消息
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_JOIN.getCommand(),
                joinMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        // ========== 发送两种类型的消息 ==========

        // 1. 发送系统通知消息 (command=5010, action="chat")
        // 用途：在聊天记录中显示"张三 加入了直播间"
        // 特点：不触发用户状态变更，仅用于聊天显示
        /*
         * String nickname = getUserNickname(req.getUserId(), req.getAppId(),
         * req.getRoomId());
         * String content = nickname + " 加入了直播间";
         * sendSystemMessage(req.getRoomId(), content, 9, req.getAppId()); // 9为加入系统消息类型
         */

        // 返回直播间信息
        return ResponseVO.successResponse(getLiveRoomInfo(req.getRoomId(), req.getAppId(), req.getUserId()));
    }

    /**
     * 离开直播间
     */
    @Override
    public ResponseVO<Void> leaveLiveRoom(String roomId, String userId, Integer appId, Integer clientType,
            String imei) {
        if (appId == null || StringUtils.isEmpty(roomId) || StringUtils.isEmpty(userId)) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.PARAM_ERROR);
        }

        // 判断是否为游客
        boolean isGuest = isUserGuest(appId, userId);

        // 游客用户：仅从Redis中清理数据，跳过数据库删除
        if (isGuest) {
            // 清理游客信息
            String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, userId);
            stringRedisTemplate.delete(guestInfoKey);

            log.info("游客用户离开直播间，已从Redis清理: roomId={}, userId={}", roomId, userId);
        } else {
            // 正式用户：不删除数据库记录，保持数据持久化完整性
            // 仅更新最后活跃时间，保留用户在直播间的历史记录
            LiveRoomMember member = liveRoomMemberMapper.selectOne(
                    Wrappers.<LiveRoomMember>lambdaQuery()
                            .eq(LiveRoomMember::getRoomId, roomId)
                            .eq(LiveRoomMember::getUserId, userId)
                            .eq(LiveRoomMember::getAppId, appId));

            if (member != null) {
                // 更新最后活跃时间，标记用户离开时间
                member.setLastActiveTime(new Date());
                member.setUpdateTime(new Date());
                liveRoomMemberMapper.updateById(member);

                log.info("正式用户离开直播间，已更新最后活跃时间，保留数据库记录: roomId={}, userId={}", roomId, userId);
            } else {
                log.warn("正式用户离开直播间时未找到数据库记录: roomId={}, userId={}", roomId, userId);
            }
        }

        // 从Redis中移除用户
        String redisKey = isGuest
                ? Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomGuestSuffix
                : Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomUserSuffix;

        // 使用StringRedisTemplate操作Redis Set
        stringRedisTemplate.opsForSet().remove(redisKey, userId);

        // 获取昵称
        String nickname = getUserNickname(userId, appId, roomId);

        // 创建离开直播间消息数据
        LiveRoomMessageData leaveMessageData = LiveRoomMessageData.createLeaveMessage(
                roomId, userId, nickname, "user_exit");

        // 使用新的标准格式发送方法
        liveRoomMessageProducer.sendMessage(
                roomId,
                LiveRoomCommand.LIVE_ROOM_LEAVE.getCommand(),
                leaveMessageData,
                appId,
                clientType,
                imei);

        /*
         * // 发送离开消息，使用用户昵称
         * String nickname = getUserNickname(userId, appId, roomId);
         * String content = nickname + " 离开了直播间";
         * sendSystemMessage(roomId, content, 10, appId); // 10为离开消息类型
         */

        return ResponseVO.successResponse();
    }

    /**
     * 发送直播间消息
     */
    @Override
    public ResponseVO<String> sendMessage(SendLiveRoomMsgReq req) {
        if (req.getAppId() == null || StringUtils.isEmpty(req.getRoomId()) || StringUtils.isEmpty(req.getFromId())) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.PARAM_ERROR);
        }

        // 判断直播间是否存在
        LiveRoom liveRoom = liveRoomMapper.selectOne(
                Wrappers.<LiveRoom>lambdaQuery()
                        .eq(LiveRoom::getRoomId, req.getRoomId())
                        .eq(LiveRoom::getAppId, req.getAppId()));
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        // 判断发送者是否为游客
        boolean isGuest = isUserGuest(req.getAppId(), req.getFromId());

        // 判断是否被禁言(全员禁言或个人禁言)
        if (liveRoom.getMuteAll() != null && liveRoom.getMuteAll() == 1) {
            // 全员禁言，只有主播和管理员可以发言
            LiveRoomMember member = liveRoomMemberMapper.selectOne(
                    Wrappers.<LiveRoomMember>lambdaQuery()
                            .eq(LiveRoomMember::getRoomId, req.getRoomId())
                            .eq(LiveRoomMember::getUserId, req.getFromId())
                            .eq(LiveRoomMember::getAppId, req.getAppId()));
            if (member == null || (member.getRole() != 1 && member.getRole() != 2)) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_MUTED);
            }
        }

        // 判断是否为游客且有消息类型限制
        if (isGuest) {
            // 游客用户：从Redis中获取禁言状态
            String guestInfoKey = String.format("liveroom:%s:guest:%s", req.getRoomId(), req.getFromId());
            String muteStatus = (String) stringRedisTemplate.opsForHash().get(guestInfoKey, "mute");
            String muteEndTimeStr = (String) stringRedisTemplate.opsForHash().get(guestInfoKey, "muteEndTime");

            // 检查是否被个人禁言
            if ("1".equals(muteStatus)) {
                if (muteEndTimeStr != null) {
                    try {
                        long muteEndTime = Long.parseLong(muteEndTimeStr);
                        if (muteEndTime > System.currentTimeMillis()) {
                            return ResponseVO.errorResponse(LiveRoomErrorCode.USER_MUTED);
                        } else {
                            // 禁言已过期，解除禁言
                            stringRedisTemplate.opsForHash().put(guestInfoKey, "mute", "0");
                            stringRedisTemplate.opsForHash().delete(guestInfoKey, "muteEndTime");
                        }
                    } catch (NumberFormatException e) {
                        log.warn("游客禁言时间格式错误: {}", muteEndTimeStr);
                    }
                }
            }
        } else {
            // 正式用户：从数据库中检查禁言状态
            LiveRoomMember member = liveRoomMemberMapper.selectOne(
                    Wrappers.<LiveRoomMember>lambdaQuery()
                            .eq(LiveRoomMember::getRoomId, req.getRoomId())
                            .eq(LiveRoomMember::getUserId, req.getFromId())
                            .eq(LiveRoomMember::getAppId, req.getAppId()));

            // 检查是否被个人禁言
            if (member != null && member.getMute() != null && member.getMute() == 1) {
                // 检查禁言是否已过期
                if (member.getMuteEndTime() != null && member.getMuteEndTime().after(new Date())) {
                    return ResponseVO.errorResponse(LiveRoomErrorCode.USER_MUTED);
                } else {
                    // 禁言已过期，解除禁言
                    member.setMute(0);
                    member.setMuteEndTime(null);
                    liveRoomMemberMapper.updateById(member);
                }
            }
        }

        // 生成消息ID
        String messageId = snowflakeIdWorker.nextId() + "";

        // 保存消息
        LiveRoomMessage message = new LiveRoomMessage();
        message.setMessageId(messageId);
        message.setRoomId(req.getRoomId());
        message.setFromId(req.getFromId());
        message.setFromNickname(req.getFromNickname());
        message.setFromAvatar(req.getFromAvatar());
        message.setMessageType(req.getMessageType());
        // 消息内容直接使用原始内容，编码处理在Redis存储时进行
        message.setContent(req.getContent());
        message.setSequence(redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.LiveRoomMessage));
        message.setAppId(req.getAppId());
        message.setSendTime(System.currentTimeMillis());
        message.setCreateTime(System.currentTimeMillis());

        // 扩展字段，存储其他信息
        JSONObject extra = new JSONObject();
        extra.put("isGuest", isGuest);
        if (req.getExtra() != null) {
            extra.putAll(req.getExtra());
        }
        message.setExtra(extra.toJSONString());

        liveRoomMessageMapper.insert(message);

        // 创建聊天消息数据
        LiveRoomMessageData chatMessageData = LiveRoomMessageData.createChatMessage(
                req.getRoomId(), req.getFromId(), req.getContent(), req.getMessageType(), messageId);
        chatMessageData.setFromNickname(req.getFromNickname())
                .setFromAvatar(req.getFromAvatar())
                .setSequence(message.getSequence())
                .setSendTime(message.getSendTime());

        if (req.getExtra() != null) {
            chatMessageData.setExtra(req.getExtra());
        }

        // 使用新的标准格式发送方法
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_MSG.getCommand(),
                chatMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        // 将消息添加到Redis缓存
        try {
            String redisKey = String.format("liveroom:%d:%s:messages", req.getAppId(), req.getRoomId());

            // 构建响应对象用于缓存
            LiveRoomMsgResp msgResp = new LiveRoomMsgResp();
            BeanUtil.copyProperties(message, msgResp);
            msgResp.setFromRole(getMemberRole(req.getRoomId(), req.getFromId(), req.getAppId()));

            // 使用Redisson原生API添加到Sorted Set，使用序列号作为分数
            // 使用emoji安全的序列化方法，并使用Base64编码确保安全存储
            String jsonString = FastJsonConfig.toJSONStringWithEmoji(msgResp);
            // 使用Base64编码确保emoji字符在Redis中安全存储
            String encodedJsonString = encodeForRedisStorage(jsonString);
            redisZSetService.add(redisKey, encodedJsonString, message.getSequence());

            // 修剪集合大小，只保留最近的100条消息
            long currentSize = redisZSetService.size(redisKey);
            if (currentSize > 100) {
                redisZSetService.removeRange(redisKey, 0, (int) (currentSize - 101));
            }

            // 设置或更新过期时间/临时调整为30天
            stringRedisTemplate.expire(redisKey, 30, TimeUnit.DAYS);

            log.info("消息已添加到Redis缓存: roomId={}, messageId={}", req.getRoomId(), messageId);
        } catch (Exception e) {
            log.error("更新消息缓存失败: roomId={}, messageId={}", req.getRoomId(), messageId, e);
        }

        return ResponseVO.successResponse(messageId);
    }

    /**
     * 获取直播间信息
     */
    @Override
    public ResponseVO<LiveRoomResp> getLiveRoomInfo(String roomId, Integer appId) {
        return ResponseVO.successResponse(getLiveRoomInfo(roomId, appId, null));
    }

    /**
     * 获取直播间最近消息
     */
    @Override
    public ResponseVO<List<LiveRoomMsgResp>> getRecentMessages(String roomId, Integer appId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 20; // 默认返回20条
        }

        // 检查直播间是否存在
        LiveRoom liveRoom = getLiveRoomById(roomId, appId);
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        try {
            // 从Redis缓存获取最近消息（使用Redisson原生API）
            String redisKey = String.format("liveroom:%d:%s:messages", appId, roomId);
            Collection<String> messageJsonSet = redisZSetService.reverseRange(redisKey, 0, limit - 1);

            List<LiveRoomMsgResp> respList = new ArrayList<>();

            if (messageJsonSet != null && !messageJsonSet.isEmpty()) {
                log.info("从Redis缓存获取直播间{}的消息，共{}条", roomId, messageJsonSet.size());
                for (String messageJson : messageJsonSet) {
                    // 从Redis存储解码，然后使用emoji安全的反序列化方法
                    String decodedJson = decodeFromRedisStorage(messageJson);
                    LiveRoomMsgResp msgResp = FastJsonConfig.parseObjectWithEmoji(decodedJson, LiveRoomMsgResp.class);
                    if (msgResp != null) {
                        respList.add(msgResp);
                    }
                }

                // 按时间顺序排序
                respList.sort(Comparator.comparing(LiveRoomMsgResp::getSequence));
                return ResponseVO.successResponse(respList);
            }

            // 缓存未命中，从数据库查询并更新缓存
            log.info("Redis缓存未命中，从数据库查询直播间{}的消息", roomId);
            return getRecentMessagesFromDBAndUpdateCache(roomId, appId, limit, redisKey);
        } catch (Exception e) {
            log.error("从Redis获取直播间消息失败", e);
            // 出现异常时回退到数据库查询
            return getRecentMessagesFromDB(roomId, appId, limit);
        }
    }

    /**
     * 从数据库获取消息并更新缓存
     */
    private ResponseVO<List<LiveRoomMsgResp>> getRecentMessagesFromDBAndUpdateCache(
            String roomId, Integer appId, Integer limit, String redisKey) {
        // 查询数据库
        ResponseVO<List<LiveRoomMsgResp>> resp = getRecentMessagesFromDB(roomId, appId, limit);

        if (resp.isOk() && resp.getData() != null && !resp.getData().isEmpty()) {
            // 更新Redis缓存
            try {
                // 清除旧缓存
                stringRedisTemplate.delete(redisKey);

                // 批量添加消息到缓存（使用Redisson原生API）
                // 使用emoji安全的序列化方法
                for (LiveRoomMsgResp msg : resp.getData()) {
                    String jsonString = FastJsonConfig.toJSONStringWithEmoji(msg);
                    String encodedJsonString = encodeForRedisStorage(jsonString);
                    redisZSetService.add(redisKey, encodedJsonString, msg.getSequence());
                }

                // 设置过期时间，1天
                stringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
                log.info("更新直播间{}的消息缓存成功，共{}条", roomId, resp.getData().size());
            } catch (Exception e) {
                log.error("更新直播间{}消息缓存失败", roomId, e);
            }
        }

        return resp;
    }

    /**
     * 从数据库获取最近消息
     */
    private ResponseVO<List<LiveRoomMsgResp>> getRecentMessagesFromDB(String roomId, Integer appId, Integer limit) {
        // 查询最近消息
        LambdaQueryWrapper<LiveRoomMessage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LiveRoomMessage::getRoomId, roomId);
        queryWrapper.eq(LiveRoomMessage::getAppId, appId);
        queryWrapper.orderByDesc(LiveRoomMessage::getSequence);
        queryWrapper.last("limit " + limit);
        List<LiveRoomMessage> messages = liveRoomMessageMapper.selectList(queryWrapper);

        // 转换为响应对象
        List<LiveRoomMsgResp> respList = new ArrayList<>();
        if (messages != null && !messages.isEmpty()) {
            // 查询所有消息发送者的角色信息
            Set<String> userIds = messages.stream()
                    .map(LiveRoomMessage::getFromId)
                    .collect(Collectors.toSet());

            Map<String, Integer> userRoleMap = new HashMap<>();
            if (!userIds.isEmpty()) {
                LambdaQueryWrapper<LiveRoomMember> memberQuery = Wrappers.lambdaQuery();
                memberQuery.eq(LiveRoomMember::getRoomId, roomId);
                memberQuery.eq(LiveRoomMember::getAppId, appId);
                memberQuery.in(LiveRoomMember::getUserId, userIds);
                List<LiveRoomMember> members = liveRoomMemberMapper.selectList(memberQuery);

                for (LiveRoomMember member : members) {
                    userRoleMap.put(member.getUserId(), member.getRole());
                }
            }

            for (LiveRoomMessage message : messages) {
                LiveRoomMsgResp resp = new LiveRoomMsgResp();
                BeanUtil.copyProperties(message, resp);
                resp.setFromRole(userRoleMap.getOrDefault(message.getFromId(), 4)); // 默认普通用户
                respList.add(resp);
            }

            // 按时间顺序排序
            respList.sort(Comparator.comparing(LiveRoomMsgResp::getSequence));
        }

        return ResponseVO.successResponse(respList);
    }

    /**
     * 获取用户在直播间的角色
     */
    private Integer getMemberRole(String roomId, String userId, Integer appId) {
        // 首先检查是否为游客
        boolean isGuest = isUserGuest(appId, userId);

        if (isGuest) {
            // 游客用户：从Redis中获取角色信息
            String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, userId);
            String roleStr = (String) stringRedisTemplate.opsForHash().get(guestInfoKey, "role");
            if (roleStr != null) {
                try {
                    return Integer.parseInt(roleStr);
                } catch (NumberFormatException e) {
                    log.warn("游客角色格式错误: {}", roleStr);
                }
            }
            return 5; // 默认游客角色
        } else {
            // 正式用户：从数据库中获取角色信息
            LiveRoomMember member = liveRoomMemberMapper.selectOne(
                    Wrappers.<LiveRoomMember>lambdaQuery()
                            .eq(LiveRoomMember::getRoomId, roomId)
                            .eq(LiveRoomMember::getUserId, userId)
                            .eq(LiveRoomMember::getAppId, appId));
            return member != null ? member.getRole() : 4; // 默认为普通用户
        }
    }

    /**
     * 禁言/解禁用户
     */
    @Override
    public ResponseVO<Void> muteUser(MuteUserReq req) {
        // 检查操作者权限
        LambdaQueryWrapper<LiveRoomMember> operatorQuery = Wrappers.lambdaQuery();
        operatorQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
        operatorQuery.eq(LiveRoomMember::getUserId, req.getOperater());
        operatorQuery.eq(LiveRoomMember::getAppId, req.getAppId());
        LiveRoomMember operator = liveRoomMemberMapper.selectOne(operatorQuery);

        if (operator == null || operator.getRole() > 2) {
            // 只有主播和管理员可以禁言
            return ResponseVO.errorResponse(LiveRoomErrorCode.NO_PERMISSION);
        }

        // 检查被操作用户是否为游客
        boolean isTargetGuest = isUserGuest(req.getAppId(), req.getUserId());
        Integer targetRole;

        if (isTargetGuest) {
            // 游客用户：从Redis中获取信息
            String guestInfoKey = String.format("liveroom:%s:guest:%s", req.getRoomId(), req.getUserId());
            String roleStr = (String) stringRedisTemplate.opsForHash().get(guestInfoKey, "role");
            if (roleStr == null) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.USER_NOT_IN_ROOM);
            }
            targetRole = Integer.parseInt(roleStr);
        } else {
            // 正式用户：从数据库中获取信息
            LambdaQueryWrapper<LiveRoomMember> userQuery = Wrappers.lambdaQuery();
            userQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
            userQuery.eq(LiveRoomMember::getUserId, req.getUserId());
            userQuery.eq(LiveRoomMember::getAppId, req.getAppId());
            LiveRoomMember targetMember = liveRoomMemberMapper.selectOne(userQuery);

            if (targetMember == null) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.USER_NOT_IN_ROOM);
            }
            targetRole = targetMember.getRole();
        }

        // 不能禁言角色比自己高的用户
        if (targetRole <= operator.getRole() && !req.getOperater().equals(req.getUserId())) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.CANNOT_MUTE_HIGHER_ROLE);
        }

        // 更新禁言状态
        if (isTargetGuest) {
            // 游客用户：更新Redis中的禁言状态
            String guestInfoKey = String.format("liveroom:%s:guest:%s", req.getRoomId(), req.getUserId());
            stringRedisTemplate.opsForHash().put(guestInfoKey, "mute", String.valueOf(req.getMute()));

            if (req.getMute() == 1) {
                // 设置禁言结束时间，默认30分钟
                long muteEndTime = System.currentTimeMillis() + 30 * 60 * 1000;
                stringRedisTemplate.opsForHash().put(guestInfoKey, "muteEndTime", String.valueOf(muteEndTime));
            } else {
                stringRedisTemplate.opsForHash().delete(guestInfoKey, "muteEndTime");
            }

            log.info("游客用户禁言状态已更新: roomId={}, userId={}, mute={}", req.getRoomId(), req.getUserId(), req.getMute());
        } else {
            // 正式用户：更新数据库中的禁言状态
            LambdaQueryWrapper<LiveRoomMember> userQuery = Wrappers.lambdaQuery();
            userQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
            userQuery.eq(LiveRoomMember::getUserId, req.getUserId());
            userQuery.eq(LiveRoomMember::getAppId, req.getAppId());
            LiveRoomMember targetMember = liveRoomMemberMapper.selectOne(userQuery);

            targetMember.setMute(req.getMute());
            if (req.getMute() == 1) {
                // 设置禁言结束时间，默认30分钟
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MINUTE, 30);
                targetMember.setMuteEndTime(calendar.getTime());
            } else {
                targetMember.setMuteEndTime(null);
            }
            targetMember.setUpdateTime(new Date());
            liveRoomMemberMapper.updateById(targetMember);

            log.info("正式用户禁言状态已更新: roomId={}, userId={}, mute={}", req.getRoomId(), req.getUserId(), req.getMute());
        }

        // 获取被操作用户的昵称
        String targetNickname = getUserNickname(req.getUserId(), req.getAppId(), req.getRoomId());

        // 创建禁言/解禁消息数据
        LiveRoomMessageData muteMessageData = LiveRoomMessageData.createMuteMessage(
                req.getRoomId(),
                req.getOperater(),
                req.getUserId(),
                req.getMute() == 1 ? "被管理员禁言" : "被管理员解除禁言",
                req.getMute() == 1 ? 30 * 60 : 0 // 30分钟或0（解禁）
        );

        // 设置被操作用户的昵称
        muteMessageData.setToNickname(targetNickname);

        // 发送禁言/解禁通知消息
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_MUTE.getCommand(),
                muteMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        log.info("发送禁言通知消息: roomId={}, operater={}, targetUser={}, mute={}",
                req.getRoomId(), req.getOperater(), req.getUserId(), req.getMute());

        return ResponseVO.successResponse();
    }

    /**
     * 全员禁言/解禁
     */
    @Override
    public ResponseVO<Void> muteAll(MuteAllReq req) {
        // 检查操作者权限
        LambdaQueryWrapper<LiveRoomMember> operatorQuery = Wrappers.lambdaQuery();
        operatorQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
        operatorQuery.eq(LiveRoomMember::getUserId, req.getOperater());
        operatorQuery.eq(LiveRoomMember::getAppId, req.getAppId());
        LiveRoomMember operator = liveRoomMemberMapper.selectOne(operatorQuery);

        if (operator == null || operator.getRole() > 2) {
            // 只有主播和管理员可以设置全员禁言
            return ResponseVO.errorResponse(LiveRoomErrorCode.NO_PERMISSION);
        }

        // 更新直播间禁言状态
        LiveRoom liveRoom = getLiveRoomById(req.getRoomId(), req.getAppId());
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        liveRoom.setMuteAll(req.getMute());
        liveRoom.setUpdateTime(new Date());
        liveRoomMapper.updateById(liveRoom);

        // 创建全员禁言/解禁消息数据
        LiveRoomMessageData muteAllMessageData = LiveRoomMessageData.createMuteAllMessage(
                req.getRoomId(),
                req.getOperater(),
                req.getMute() == 1);

        // 发送全员禁言/解禁通知消息
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_MUTE_ALL.getCommand(),
                muteAllMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        log.info("发送全员禁言通知消息: roomId={}, operater={}, muteAll={}",
                req.getRoomId(), req.getOperater(), req.getMute());

        return ResponseVO.successResponse();
    }

    /**
     * 更新直播间公告
     */
    @Override
    public ResponseVO<Void> updateAnnouncement(UpdateAnnouncementReq req) {
        String roomId = req.getRoomId();
        String announcement = req.getAnnouncement();
        String operatorId = req.getOperater();
        Integer appId = req.getAppId();
        // 检查操作者权限
        LambdaQueryWrapper<LiveRoomMember> operatorQuery = Wrappers.lambdaQuery();
        operatorQuery.eq(LiveRoomMember::getRoomId, roomId);
        operatorQuery.eq(LiveRoomMember::getUserId, operatorId);
        operatorQuery.eq(LiveRoomMember::getAppId, appId);
        LiveRoomMember operator = liveRoomMemberMapper.selectOne(operatorQuery);

        if (operator == null || operator.getRole() > 2) {
            // 只有主播和管理员可以更新公告
            return ResponseVO.errorResponse(LiveRoomErrorCode.NO_PERMISSION);
        }

        // 更新直播间公告
        LiveRoom liveRoom = getLiveRoomById(roomId, appId);
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        liveRoom.setAnnouncement(announcement);
        liveRoom.setUpdateTime(new Date());
        liveRoomMapper.updateById(liveRoom);

        /*
         * // 发送系统消息
         * String content = "管理员更新了直播间公告";
         * sendSystemMessage(roomId, content, 8, appId);
         */

        // 创建公告消息数据
        LiveRoomMessageData announcementMessageData = LiveRoomMessageData.createAnnouncementMessage(
                roomId, operatorId, announcement);

        // 使用新的标准格式发送方法
        liveRoomMessageProducer.sendMessage(
                roomId,
                LiveRoomCommand.LIVE_ROOM_ANNOUNCEMENT.getCommand(),
                announcementMessageData,
                appId,
                req.getClientType(),
                req.getImei());

        return ResponseVO.successResponse();
    }

    /**
     * 获取直播间信息（内部方法）
     */
    private LiveRoomResp getLiveRoomInfo(String roomId, Integer appId, String userId) {
        LiveRoom liveRoom = getLiveRoomById(roomId, appId);
        if (liveRoom == null) {
            return null;
        }

        LiveRoomResp resp = new LiveRoomResp();
        BeanUtil.copyProperties(liveRoom, resp);

        // 获取主播信息
        ResponseVO<Map<String, Object>> anchorInfo = imUserService.getUserInfo(liveRoom.getAnchorId(), appId);
        if (anchorInfo.isOk() && anchorInfo.getData() != null) {
            resp.setAnchorName((String) anchorInfo.getData().get("nickName"));
            resp.setAnchorAvatar((String) anchorInfo.getData().get("photo"));
        }

        // 获取在线人数（正式用户 + 游客用户）- 优化：完全从Redis获取实时在线数据
        // 1. 统计Redis中的正式用户数量（实时在线）
        String normalUserRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                + Constants.RedisConstants.LiveRoomUserSuffix;
        Long normalUserCount = stringRedisTemplate.opsForSet().size(normalUserRedisKey);
        int normalUserOnlineCount = normalUserCount != null ? normalUserCount.intValue() : 0;

        // 2. 统计Redis中的游客用户数量（实时在线）
        String guestRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                + Constants.RedisConstants.LiveRoomGuestSuffix;
        Long guestCount = stringRedisTemplate.opsForSet().size(guestRedisKey);
        int guestUserOnlineCount = guestCount != null ? guestCount.intValue() : 0;

        // 3. 总在线人数 = 正式用户在线数 + 游客用户在线数
        int totalOnlineCount = normalUserOnlineCount + guestUserOnlineCount;
        resp.setOnlineCount(totalOnlineCount);

        log.debug("直播间实时在线人数统计: roomId={}, 正式用户在线={}, 游客用户在线={}, 总计={}",
                roomId, normalUserOnlineCount, guestUserOnlineCount, totalOnlineCount);

        return resp;
    }

    /**
     * 根据ID获取直播间
     */
    private LiveRoom getLiveRoomById(String roomId, Integer appId) {
        if (StrUtil.isEmpty(roomId)) {
            return null;
        }

        LambdaQueryWrapper<LiveRoom> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LiveRoom::getRoomId, roomId);
        queryWrapper.eq(LiveRoom::getAppId, appId);
        return liveRoomMapper.selectOne(queryWrapper);
    }

    /**
     * 获取用户昵称
     * 
     * @param userId 用户ID
     * @param appId  应用ID
     * @param roomId 直播间ID（可选，用于获取游客昵称）
     * @return 用户昵称，如果获取失败则返回默认值
     */
    private String getUserNickname(String userId, Integer appId, String roomId) {
        if (StringUtils.isEmpty(userId)) {
            return "用户";
        }

        try {
            // 判断是否为游客用户
            boolean isGuest = isUserGuest(appId, userId);

            if (isGuest) {
                // 游客用户：直接使用userId
                return userId;
            } else {
                // 正式用户：从用户服务获取昵称
                ResponseVO<Map<String, Object>> userInfoResp = imUserService.getUserInfo(userId, appId);
                if (userInfoResp.isOk() && userInfoResp.getData() != null) {
                    String nickname = (String) userInfoResp.getData().get("nickName");
                    return StringUtils.isNotEmpty(nickname) ? nickname : userId;
                }
            }
        } catch (Exception e) {
            log.warn("获取用户昵称失败: userId={}, appId={}, roomId={}, error={}", userId, appId, roomId, e.getMessage());
        }

        // 如果获取失败，返回userId作为默认值
        return userId;
    }

    /**
     * 获取用户昵称（重载方法，不需要roomId）
     * 
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 用户昵称，如果获取失败则返回默认值
     */
    private String getUserNickname(String userId, Integer appId) {
        return getUserNickname(userId, appId, null);
    }

    /**
     * 发送系统消息
     */
    private void sendSystemMessage(String roomId, String content, Integer messageType, Integer appId) {
        // 生成消息ID和序列号
        String messageId = snowflakeIdWorker.nextId() + "";
        Long sequence = redisSeq.doGetSeq(appId + ":" + Constants.SeqConstants.LiveRoomMessage);

        // 保存系统消息
        LiveRoomMessage message = new LiveRoomMessage();
        message.setMessageId(messageId);
        message.setRoomId(roomId);
        message.setFromId("system");
        message.setFromNickname("系统通知");
        message.setMessageType(messageType);
        message.setContent(content);
        message.setSequence(sequence);
        message.setAppId(appId);
        message.setSendTime(System.currentTimeMillis());
        message.setCreateTime(System.currentTimeMillis());
        liveRoomMessageMapper.insert(message);

        // 创建系统消息数据
        LiveRoomMessageData systemMessageData = LiveRoomMessageData.createChatMessage(
                roomId, "system", content, messageType, messageId);
        systemMessageData.setFromNickname("系统通知")
                .setFromRole(0)
                .setSequence(sequence)
                .setSendTime(message.getSendTime());

        // 使用新的标准格式发送方法
        liveRoomMessageProducer.sendMessage(
                roomId,
                LiveRoomCommand.LIVE_ROOM_MSG.getCommand(),
                systemMessageData,
                appId,
                0, // 系统消息不需要客户端类型
                null // 系统消息不需要设备标识
        );

        // 构建响应对象用于缓存
        LiveRoomMsgResp msgResp = new LiveRoomMsgResp();
        BeanUtil.copyProperties(message, msgResp);
        msgResp.setFromRole(0); // 系统角色

        // 将系统消息添加到Redis缓存
        try {
            String redisKey = String.format("liveroom:%d:%s:messages", appId, roomId);

            // 使用Redisson原生API添加到Sorted Set
            // 使用emoji安全的序列化方法
            String jsonString = FastJsonConfig.toJSONStringWithEmoji(msgResp);
            String encodedJsonString = encodeForRedisStorage(jsonString);
            redisZSetService.add(redisKey, encodedJsonString, message.getSequence());

            // 修剪集合大小，只保留最近的200条消息
            long currentSize = redisZSetService.size(redisKey);
            if (currentSize > 200) {
                redisZSetService.removeRange(redisKey, 0, (int) (currentSize - 201));
            }

            // 更新过期时间
            stringRedisTemplate.expire(redisKey, 1, TimeUnit.DAYS);

            log.info("系统消息已添加到Redis缓存: roomId={}, messageId={}", roomId, messageId);
        } catch (Exception e) {
            log.error("更新系统消息缓存失败: roomId={}, messageId={}", roomId, messageId, e);
            // 仅记录日志，不影响主流程
        }
    }

    /**
     * 主播创建直播间后自动加入聊天室
     * 参考加入直播间的实现方式，独立实现主播自动加入聊天室功能
     * 不调用joinLiveRoom()方法，而是参考其内部逻辑实现相同效果
     *
     * @param roomId     直播间ID
     * @param anchorId   主播用户ID
     * @param appId      应用ID
     * @param clientType 客户端类型
     * @param imei       设备标识
     */
    private void autoJoinAnchorToChatRoom(String roomId, String anchorId, Integer appId,
            Integer clientType, String imei) {
        try {
            log.info("主播自动加入聊天室开始: roomId={}, anchorId={}", roomId, anchorId);

            // 1. 获取主播用户信息
            String anchorNickname = "主播";
            String anchorAvatar = "";

            ResponseVO<Map<String, Object>> userInfoResp = imUserService.getUserInfo(anchorId, appId);
            if (userInfoResp.isOk() && userInfoResp.getData() != null) {
                anchorNickname = (String) userInfoResp.getData().get("nickName");
                anchorAvatar = (String) userInfoResp.getData().get("photo");
                if (anchorNickname == null)
                    anchorNickname = "主播";
                if (anchorAvatar == null)
                    anchorAvatar = "";
                log.debug("成功获取主播用户信息: anchorId={}, nickname={}", anchorId, anchorNickname);
            } else {
                log.warn("获取主播用户信息失败，使用默认值: anchorId={}, response={}",
                        anchorId, userInfoResp != null ? userInfoResp.getMsg() : "null");
            }

            // 2. 发送结构化创建消息 (command=5020, action="create")
            // 用途：客户端更新在线用户列表和用户状态管理
            // 特点：触发SessionSocketHolder.addUserToRoom()操作，包含主播详细信息
            LiveRoomMessageData createMessageData = LiveRoomMessageData.createAnchorJoinMessage(
                    roomId, anchorId, anchorNickname, anchorAvatar, 1 // role=1主播
            );

            log.info("发送结构化创建消息: roomId={}, userId={}, nickname={}, command={}",
                    roomId, anchorId, anchorNickname, LiveRoomCommand.LIVE_ROOM_CREATE.getCommand());

            // 发送结构化创建消息 (使用command=5020)
            liveRoomMessageProducer.sendMessage(
                    roomId,
                    LiveRoomCommand.LIVE_ROOM_CREATE.getCommand(),
                    createMessageData,
                    appId,
                    clientType,
                    imei);

            // ========== 发送系统通知消息（可选） ==========
            // 3. 发送系统通知消息 (command=5010, action="chat")
            // 用途：在聊天记录中显示"主播 开启了直播间"
            // 特点：不触发用户状态变更，仅用于聊天显示
            /*
             * String content = anchorNickname + " 开启了直播间";
             * sendSystemMessage(roomId, content, 8, appId); // 8为系统消息类型
             */

            /*
             * log.info("主播自动加入聊天室成功: roomId={}, anchorId={}, nickname={}",
             * roomId, anchorId, anchorNickname);
             */

        } catch (Exception e) {
            log.error("主播自动加入聊天室失败: roomId={}, anchorId={}", roomId, anchorId, e);
        }
    }

    /**
     * 获取单个直播间的在线用户列表
     * 直接从Redis缓存中读取在线用户信息，不查询数据库
     */
    @Override
    public ResponseVO<List<LiveRoomUserResp>> getLiveRoomOnlineUsers(String roomId, Integer appId) {
        if (StringUtils.isEmpty(roomId) || appId == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.PARAM_ERROR);
        }

        // 检查直播间是否存在
        LiveRoom liveRoom = getLiveRoomById(roomId, appId);
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        List<LiveRoomUserResp> userRespList = new ArrayList<>();

        try {
            // 1. 从Redis获取正式用户列表
            String normalUserRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomUserSuffix;
            Set<String> normalUserIds = stringRedisTemplate.opsForSet().members(normalUserRedisKey);

            // 2. 从Redis获取游客用户列表
            String guestUserRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomGuestSuffix;
            Set<String> guestUserIds = stringRedisTemplate.opsForSet().members(guestUserRedisKey);

            // 3. 处理正式用户 - 从数据库获取详细信息
            if (normalUserIds != null && !normalUserIds.isEmpty()) {
                LambdaQueryWrapper<LiveRoomMember> memberQuery = Wrappers.lambdaQuery();
                memberQuery.eq(LiveRoomMember::getRoomId, roomId);
                memberQuery.eq(LiveRoomMember::getAppId, appId);
                memberQuery.in(LiveRoomMember::getUserId, normalUserIds);
                List<LiveRoomMember> members = liveRoomMemberMapper.selectList(memberQuery);

                // 批量获取用户信息
                Map<String, ImUserDataEntity> userDataMap = new HashMap<>();
                if (!normalUserIds.isEmpty()) {
                    try {
                        GetUserInfoReq userInfoReq = new GetUserInfoReq();
                        userInfoReq.setUserIds(new ArrayList<>(normalUserIds));
                        userInfoReq.setAppId(appId);
                        ResponseVO<GetUserInfoResp> userInfoResp = imUserService.getUserInfo(userInfoReq);

                        if (userInfoResp.isOk() && userInfoResp.getData() != null
                                && userInfoResp.getData().getUserDataItem() != null) {
                            userDataMap = userInfoResp.getData().getUserDataItem().stream()
                                    .collect(Collectors.toMap(
                                            ImUserDataEntity::getUserId,
                                            userData -> userData));
                        }
                    } catch (Exception e) {
                        log.error("批量获取正式用户信息失败", e);
                    }
                }

                // 构建正式用户响应
                for (LiveRoomMember member : members) {
                    LiveRoomUserResp userResp = new LiveRoomUserResp();
                    userResp.setUserId(member.getUserId());
                    userResp.setRole(member.getRole());
                    userResp.setMuted(member.getMute());
                    userResp.setJoinTime(member.getJoinTime());

                    // 从用户数据中获取信息
                    ImUserDataEntity userData = userDataMap.get(member.getUserId());
                    if (userData != null) {
                        userResp.setNickname(userData.getNickName());
                        userResp.setAvatar(userData.getPhoto());
                    } else {
                        // 使用成员表中的信息作为备用
                        userResp.setNickname(member.getNickname());
                        userResp.setAvatar(member.getAvatar());
                    }

                    userRespList.add(userResp);
                }
            }

            // 4. 处理游客用户 - 从Redis获取详细信息
            if (guestUserIds != null && !guestUserIds.isEmpty()) {
                for (String guestId : guestUserIds) {
                    LiveRoomUserResp userResp = new LiveRoomUserResp();
                    userResp.setUserId(guestId);
                    userResp.setRole(5); // 游客角色

                    // 从Redis中获取游客详细信息
                    String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, guestId);
                    Map<Object, Object> guestInfo = stringRedisTemplate.opsForHash().entries(guestInfoKey);

                    if (!guestInfo.isEmpty()) {
                        userResp.setNickname((String) guestInfo.get("nickname"));
                        userResp.setAvatar((String) guestInfo.get("avatar"));

                        // 解析禁言状态
                        String muteStr = (String) guestInfo.get("mute");
                        userResp.setMuted("1".equals(muteStr) ? 1 : 0);

                        // 解析加入时间
                        String joinTimeStr = (String) guestInfo.get("joinTime");
                        if (joinTimeStr != null) {
                            try {
                                long joinTimeMillis = Long.parseLong(joinTimeStr);
                                userResp.setJoinTime(new Date(joinTimeMillis));
                            } catch (NumberFormatException e) {
                                log.warn("游客加入时间格式错误: {}", joinTimeStr);
                                userResp.setJoinTime(new Date());
                            }
                        }
                    } else {
                        // 备用信息
                        userResp.setNickname(guestId);
                        userResp.setAvatar("");
                        userResp.setMuted(0);
                        userResp.setJoinTime(new Date());
                    }

                    userRespList.add(userResp);
                }
            }

            log.info("获取直播间在线用户列表成功: roomId={}, 正式用户数={}, 游客用户数={}, 总计={}",
                    roomId,
                    normalUserIds != null ? normalUserIds.size() : 0,
                    guestUserIds != null ? guestUserIds.size() : 0,
                    userRespList.size());

            return ResponseVO.successResponse(userRespList);

        } catch (Exception e) {
            log.error("获取直播间在线用户列表失败: roomId={}, appId={}", roomId, appId, e);
            return ResponseVO.errorResponse(LiveRoomErrorCode.GET_ONLINE_USERS_FAILED);
        }
    }

    /**
     * 批量获取多个直播间的用户列表信息
     */
    @Override
    public ResponseVO<Map<String, List<LiveRoomUserResp>>> batchGetLiveRoomUsers(List<String> roomIds, Integer appId) {
        if (roomIds == null || roomIds.isEmpty()) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_IDS_EMPTY);
        }

        // 检查直播间是否存在
        LambdaQueryWrapper<LiveRoom> roomQueryWrapper = Wrappers.lambdaQuery();
        roomQueryWrapper.in(LiveRoom::getRoomId, roomIds);
        roomQueryWrapper.eq(LiveRoom::getAppId, appId);
        List<LiveRoom> liveRooms = liveRoomMapper.selectList(roomQueryWrapper);

        if (liveRooms.isEmpty()) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOMS_NOT_FOUND);
        }

        // 从Redis获取所有直播间的在线正式用户
        Map<String, Set<String>> normalUsersByRoom = new HashMap<>();
        for (String roomId : roomIds) {
            String normalUserRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomUserSuffix;
            Set<String> normalUserIds = stringRedisTemplate.opsForSet().members(normalUserRedisKey);
            if (normalUserIds != null && !normalUserIds.isEmpty()) {
                normalUsersByRoom.put(roomId, normalUserIds);
            }
        }

        // 获取所有在线正式用户的详细信息（从数据库）
        Set<String> allNormalUserIds = normalUsersByRoom.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        Map<String, LiveRoomMember> memberDataMap = new HashMap<>();
        if (!allNormalUserIds.isEmpty()) {
            LambdaQueryWrapper<LiveRoomMember> memberQueryWrapper = Wrappers.lambdaQuery();
            memberQueryWrapper.in(LiveRoomMember::getUserId, allNormalUserIds);
            memberQueryWrapper.eq(LiveRoomMember::getAppId, appId);
            List<LiveRoomMember> allMembers = liveRoomMemberMapper.selectList(memberQueryWrapper);

            // 构建用户ID到成员信息的映射
            memberDataMap = allMembers.stream()
                    .collect(Collectors.toMap(
                            member -> member.getRoomId() + ":" + member.getUserId(),
                            member -> member));
        }

        // 获取所有直播间的游客用户
        Map<String, Set<String>> guestsByRoom = new HashMap<>();
        Map<String, Map<String, Map<String, String>>> guestInfoByRoom = new HashMap<>();

        for (String roomId : roomIds) {
            // 获取游客用户ID列表
            String guestRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomGuestSuffix;
            Set<String> guestIds = stringRedisTemplate.opsForSet().members(guestRedisKey);
            if (guestIds != null && !guestIds.isEmpty()) {
                guestsByRoom.put(roomId, guestIds);

                // 获取游客详细信息
                Map<String, Map<String, String>> roomGuestInfo = new HashMap<>();
                for (String guestId : guestIds) {
                    String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, guestId);
                    Map<Object, Object> guestInfo = stringRedisTemplate.opsForHash().entries(guestInfoKey);
                    if (!guestInfo.isEmpty()) {
                        Map<String, String> guestData = new HashMap<>();
                        guestInfo.forEach((k, v) -> guestData.put(k.toString(), v.toString()));
                        roomGuestInfo.put(guestId, guestData);
                    }
                }
                guestInfoByRoom.put(roomId, roomGuestInfo);
            }
        }

        // 收集所有用户ID（正式用户 + 游客用户），用于批量获取用户信息
        List<String> allUserIds = new ArrayList<>();

        // 添加正式用户ID
        allUserIds.addAll(allNormalUserIds);

        // 添加游客用户ID
        guestsByRoom.values().forEach(guestIds -> allUserIds.addAll(guestIds));

        // 批量获取用户信息并直接映射到用户ID
        Map<String, ImUserDataEntity> userDataMap = new HashMap<>();
        if (!allUserIds.isEmpty()) {
            try {
                // 使用批量获取用户信息接口
                GetUserInfoReq userInfoReq = new GetUserInfoReq();
                userInfoReq.setUserIds(allUserIds);
                userInfoReq.setAppId(appId);
                ResponseVO<GetUserInfoResp> userInfoResp = imUserService.getUserInfo(userInfoReq);

                if (userInfoResp.isOk() && userInfoResp.getData() != null
                        && userInfoResp.getData().getUserDataItem() != null) {
                    // 直接将用户数据映射到用户ID
                    userDataMap = userInfoResp.getData().getUserDataItem().stream()
                            .collect(Collectors.toMap(
                                    ImUserDataEntity::getUserId,
                                    userData -> userData));
                }
            } catch (Exception e) {
                log.error("批量获取用户信息失败", e);
            }
        }

        // 转换为响应对象
        Map<String, List<LiveRoomUserResp>> result = new HashMap<>(roomIds.size());

        for (String roomId : roomIds) {
            List<LiveRoomUserResp> userRespList = new ArrayList<>();

            // 1. 处理正式用户
            Set<String> normalUsers = normalUsersByRoom.getOrDefault(roomId, Collections.emptySet());
            for (String userId : normalUsers) {
                String memberKey = roomId + ":" + userId;
                LiveRoomMember member = memberDataMap.get(memberKey);

                LiveRoomUserResp userResp = new LiveRoomUserResp();
                userResp.setUserId(userId);

                if (member != null) {
                    userResp.setRole(member.getRole());
                    userResp.setMuted(member.getMute());
                    userResp.setJoinTime(member.getJoinTime());
                } else {
                    // 如果数据库中没有找到成员信息，设置默认值
                    userResp.setRole(4); // 默认为普通用户
                    userResp.setMuted(0); // 默认不禁言
                    userResp.setJoinTime(new Date());
                }

                // 从用户数据中获取信息
                ImUserDataEntity userData = userDataMap.get(userId);
                if (userData != null) {
                    userResp.setNickname(userData.getNickName());
                    userResp.setAvatar(userData.getPhoto());
                } else if (member != null) {
                    // 使用成员表中的信息作为备用
                    userResp.setNickname(member.getNickname());
                    userResp.setAvatar(member.getAvatar());
                } else {
                    // 设置默认值
                    userResp.setNickname("用户" + userId);
                    userResp.setAvatar("");
                }

                userRespList.add(userResp);
            }

            // 2. 处理游客用户
            Set<String> guestIds = guestsByRoom.getOrDefault(roomId, Collections.emptySet());
            Map<String, Map<String, String>> roomGuestInfo = guestInfoByRoom.getOrDefault(roomId,
                    Collections.emptyMap());

            for (String guestId : guestIds) {
                LiveRoomUserResp userResp = new LiveRoomUserResp();
                userResp.setUserId(guestId);
                userResp.setRole(5); // 游客角色

                // 从Redis中获取游客信息
                Map<String, String> guestData = roomGuestInfo.get(guestId);
                if (guestData != null) {
                    userResp.setNickname(guestData.get("nickname"));
                    userResp.setAvatar(guestData.get("avatar"));

                    // 解析禁言状态
                    String muteStr = guestData.get("mute");
                    userResp.setMuted("1".equals(muteStr) ? 1 : 0);

                    // 解析加入时间
                    String joinTimeStr = guestData.get("joinTime");
                    if (joinTimeStr != null) {
                        try {
                            long joinTimeMillis = Long.parseLong(joinTimeStr);
                            userResp.setJoinTime(new Date(joinTimeMillis));
                        } catch (NumberFormatException e) {
                            log.warn("游客加入时间格式错误: {}", joinTimeStr);
                            userResp.setJoinTime(new Date());
                        }
                    }
                } else {
                    // 备用信息
                    userResp.setNickname(guestId);
                    userResp.setAvatar("");
                    userResp.setMuted(0);
                    userResp.setJoinTime(new Date());
                }

                userRespList.add(userResp);
            }

            result.put(roomId, userRespList);
        }

        return ResponseVO.successResponse(result);
    }

    /**
     * 踢出用户
     */
    @Override
    public ResponseVO<Void> kickUser(KickUserReq req) {
        // 检查操作者权限
        LambdaQueryWrapper<LiveRoomMember> operatorQuery = Wrappers.lambdaQuery();
        operatorQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
        operatorQuery.eq(LiveRoomMember::getUserId, req.getOperater());
        operatorQuery.eq(LiveRoomMember::getAppId, req.getAppId());
        LiveRoomMember operator = liveRoomMemberMapper.selectOne(operatorQuery);

        if (operator == null || operator.getRole() > 2) {
            // 只有主播和管理员可以踢人
            return ResponseVO.errorResponse(LiveRoomErrorCode.NO_PERMISSION);
        }

        // 检查被踢用户是否为游客
        boolean isTargetGuest = isUserGuest(req.getAppId(), req.getUserId());

        if (isTargetGuest) {
            // 游客用户：从Redis中移除
            String guestRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + req.getRoomId()
                    + Constants.RedisConstants.LiveRoomGuestSuffix;
            stringRedisTemplate.opsForSet().remove(guestRedisKey, req.getUserId());

            // 删除游客详细信息
            String guestInfoKey = String.format("liveroom:%s:guest:%s", req.getRoomId(), req.getUserId());
            stringRedisTemplate.delete(guestInfoKey);

            log.info("游客用户 {} 被踢出直播间 {}", req.getUserId(), req.getRoomId());
        } else {
            // 正式用户：从数据库中删除
            LambdaQueryWrapper<LiveRoomMember> targetQuery = Wrappers.lambdaQuery();
            targetQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
            targetQuery.eq(LiveRoomMember::getUserId, req.getUserId());
            targetQuery.eq(LiveRoomMember::getAppId, req.getAppId());
            LiveRoomMember targetMember = liveRoomMemberMapper.selectOne(targetQuery);

            if (targetMember == null) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.USER_NOT_IN_ROOM);
            }

            if (targetMember.getRole() <= operator.getRole() && !targetMember.getUserId().equals(req.getOperater())) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.CANNOT_KICK_HIGHER_ROLE);
            }

            liveRoomMemberMapper.delete(targetQuery);
            log.info("正式用户 {} 被踢出直播间 {}", req.getUserId(), req.getRoomId());
        }

        /*
         * // 发送系统消息，使用用户昵称
         * String nickname = getUserNickname(req.getUserId(), req.getAppId(),
         * req.getRoomId());
         * String content = nickname + " 被管理员踢出直播间";
         * sendSystemMessage(req.getRoomId(), content, 8, req.getAppId());
         */

        // 获取被踢用户的昵称
        String targetNickname = getUserNickname(req.getUserId(), req.getAppId(), req.getRoomId());

        // 创建踢人消息数据
        LiveRoomMessageData kickMessageData = LiveRoomMessageData.createKickMessage(
                req.getRoomId(), req.getOperater(), req.getUserId(),
                req.getReason() != null ? req.getReason() : "被管理员踢出直播间");

        // 设置被踢用户的昵称
        kickMessageData.setToNickname(targetNickname);

        // 发送踢人通知消息
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_KICK.getCommand(),
                kickMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        log.info("发送踢人通知消息: roomId={}, operater={}, targetUser={}, targetNickname={}",
                req.getRoomId(), req.getOperater(), req.getUserId(), targetNickname);

        return ResponseVO.successResponse();
    }

    /**
     * 关闭直播间
     */
    @Override
    public ResponseVO<Void> closeLiveRoom(CloseLiveRoomReq req) {
        // 检查操作者权限
        LambdaQueryWrapper<LiveRoomMember> operatorQuery = Wrappers.lambdaQuery();
        operatorQuery.eq(LiveRoomMember::getRoomId, req.getRoomId());
        operatorQuery.eq(LiveRoomMember::getUserId, req.getOperater());
        operatorQuery.eq(LiveRoomMember::getAppId, req.getAppId());
        LiveRoomMember operator = liveRoomMemberMapper.selectOne(operatorQuery);

        if (operator == null || operator.getRole() != 1) {
            // 只有主播可以关闭直播间
            return ResponseVO.errorResponse(LiveRoomErrorCode.NO_PERMISSION);
        }

        // 更新直播间状态
        LiveRoom liveRoom = getLiveRoomById(req.getRoomId(), req.getAppId());
        if (liveRoom == null) {
            return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_NOT_EXIST);
        }

        liveRoom.setStatus(2); // 已结束
        liveRoom.setUpdateTime(new Date());
        liveRoomMapper.updateById(liveRoom);

        // 发送系统消息
        String content = req.getReason() != null ? req.getReason() : "主播关闭了直播间";
        sendSystemMessage(req.getRoomId(), content, 8, req.getAppId());

        // 创建关闭直播间消息数据
        LiveRoomMessageData closeMessageData = LiveRoomMessageData.createCloseMessage(
                req.getRoomId(), req.getOperater(), content);

        // 使用请求模型中的clientType和imei参数，发送关闭直播间消息
        liveRoomMessageProducer.sendMessage(
                req.getRoomId(),
                LiveRoomCommand.LIVE_ROOM_CLOSE.getCommand(),
                closeMessageData,
                req.getAppId(),
                req.getClientType(),
                req.getImei());

        // 清空直播间Redis缓存数据
        clearLiveRoomRedisCache(req.getRoomId(), req.getAppId());

        return ResponseVO.successResponse();
    }

    /**
     * 离开直播间
     */
    @Override
    public ResponseVO<Void> leaveLiveRoom(LeaveLiveRoomReq req) {
        return leaveLiveRoom(req.getRoomId(), req.getUserId(), req.getAppId(), req.getClientType(), req.getImei());
    }

    /**
     * 清空直播间Redis缓存数据
     * 包括：在线用户列表、游客列表、历史消息缓存、游客详细信息等
     */
    private void clearLiveRoomRedisCache(String roomId, Integer appId) {
        try {
            log.info("开始清空直播间Redis缓存: roomId={}, appId={}", roomId, appId);

            // 1. 清空正式用户在线列表
            String userRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomUserSuffix;
            Set<String> users = stringRedisTemplate.opsForSet().members(userRedisKey);
            if (users != null && !users.isEmpty()) {
                log.info("清空正式用户在线列表，共{}个用户: {}", users.size(), users);
                stringRedisTemplate.delete(userRedisKey);
            }

            // 2. 清空游客在线列表
            String guestRedisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                    + Constants.RedisConstants.LiveRoomGuestSuffix;
            Set<String> guests = stringRedisTemplate.opsForSet().members(guestRedisKey);
            if (guests != null && !guests.isEmpty()) {
                log.info("清空游客在线列表，共{}个游客: {}", guests.size(), guests);

                // 清空每个游客的详细信息
                for (String guestId : guests) {
                    String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, guestId);
                    stringRedisTemplate.delete(guestInfoKey);
                }

                // 清空游客列表
                stringRedisTemplate.delete(guestRedisKey);
            }

            // 3. 清空历史消息缓存
            String messageRedisKey = String.format("liveroom:%d:%s:messages", appId, roomId);
            long messageCount = redisZSetService.size(messageRedisKey);
            if (messageCount > 0) {
                log.info("清空历史消息缓存，共{}条消息", messageCount);
                stringRedisTemplate.delete(messageRedisKey);
            }

            log.info("直播间Redis缓存清空完成: roomId={}, 清理了{}个正式用户, {}个游客, {}条历史消息",
                    roomId, users != null ? users.size() : 0, guests != null ? guests.size() : 0, messageCount);

        } catch (Exception e) {
            log.error("清空直播间Redis缓存失败: roomId={}, appId={}", roomId, appId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 批量检查直播间是否存在
     */
    @Override
    public ResponseVO<List<LiveRoomExistsResp>> checkLiveRoomExists(CheckLiveRoomExistsReq req) {
        try {
            // 参数验证
            if (req.getRoomIds() == null || req.getRoomIds().isEmpty()) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_IDS_EMPTY);
            }

            // 检查数量限制
            if (req.getRoomIds().size() > 100) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.ROOM_IDS_LIMIT_EXCEEDED);
            }

            // 去重处理
            List<String> uniqueRoomIds = req.getRoomIds().stream()
                    .filter(roomId -> !StringUtils.isEmpty(roomId))
                    .distinct()
                    .collect(Collectors.toList());

            if (uniqueRoomIds.isEmpty()) {
                return ResponseVO.errorResponse(LiveRoomErrorCode.VALID_ROOM_IDS_EMPTY);
            }

            // 查询数据库中存在的直播间
            LambdaQueryWrapper<LiveRoom> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(LiveRoom::getRoomId, uniqueRoomIds);
            queryWrapper.eq(LiveRoom::getAppId, req.getAppId());
            queryWrapper.select(LiveRoom::getRoomId, LiveRoom::getStatus, LiveRoom::getRoomName);

            List<LiveRoom> existingRooms = liveRoomMapper.selectList(queryWrapper);

            // 构建存在的直播间映射
            Map<String, LiveRoom> existingRoomMap = existingRooms.stream()
                    .collect(Collectors.toMap(LiveRoom::getRoomId, room -> room));

            // 构建响应结果
            List<LiveRoomExistsResp> result = uniqueRoomIds.stream()
                    .map(roomId -> {
                        LiveRoom room = existingRoomMap.get(roomId);
                        if (room != null) {
                            // 直播间存在，返回详细信息
                            return new LiveRoomExistsResp(roomId, true, room.getStatus(), room.getRoomName());
                        } else {
                            // 直播间不存在
                            return new LiveRoomExistsResp(roomId, false);
                        }
                    })
                    .collect(Collectors.toList());

            log.debug("批量检查直播间存在状态完成: appId={}, 查询数量={}, 存在数量={}",
                    req.getAppId(), uniqueRoomIds.size(), existingRooms.size());

            return ResponseVO.successResponse(result);

        } catch (Exception e) {
            log.error("批量检查直播间是否存在失败: appId={}, roomIds={}", req.getAppId(), req.getRoomIds(), e);
            return ResponseVO.errorResponse(LiveRoomErrorCode.BATCH_CHECK_FAILED);
        }
    }

    /**
     * 通过Redis判断用户是否在直播间在线
     * 这是判断用户真实在线状态的准确方法，而不是数据库中的历史记录
     *
     * @param roomId  直播间ID
     * @param userId  用户ID
     * @param isGuest 是否为游客
     * @return 是否在线
     */
    private boolean isUserOnlineInRoom(String roomId, String userId, boolean isGuest) {
        try {
            // 根据用户类型选择对应的Redis键
            String redisKey = isGuest
                    ? Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                            + Constants.RedisConstants.LiveRoomGuestSuffix
                    : Constants.RedisConstants.LiveRoomGuestPrefix + roomId
                            + Constants.RedisConstants.LiveRoomUserSuffix;

            // 检查用户是否在Redis在线用户集合中
            Boolean isMember = stringRedisTemplate.opsForSet().isMember(redisKey, userId);

            log.debug("检查用户在线状态: roomId={}, userId={}, 用户类型={}, 在线状态={}",
                    roomId, userId, isGuest ? "游客" : "正式用户", isMember);

            return isMember != null && isMember;

        } catch (Exception e) {
            log.error("检查用户在线状态失败: roomId={}, userId={}, isGuest={}", roomId, userId, isGuest, e);
            // 异常时返回false，按新用户处理
            return false;
        }
    }

    /**
     * 为Redis存储编码字符串，使用Base64确保emoji字符安全存储
     *
     * @param text 原始字符串
     * @return Base64编码的字符串
     */
    private String encodeForRedisStorage(String text) {
        if (text == null) {
            return null;
        }

        try {
            // 使用Base64编码确保emoji字符在Redis中安全存储
            byte[] utf8Bytes = text.getBytes(StandardCharsets.UTF_8);
            return Base64.getEncoder().encodeToString(utf8Bytes);
        } catch (Exception e) {
            log.error("Base64编码失败: {}", text, e);
            return text; // 返回原始字符串作为降级方案
        }
    }

    /**
     * 从Redis存储解码字符串，解码Base64确保emoji字符正确恢复
     *
     * @param encodedText Base64编码的字符串
     * @return 解码后的原始字符串
     */
    private String decodeFromRedisStorage(String encodedText) {
        if (encodedText == null) {
            return null;
        }

        try {
            // 尝试Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(encodedText);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // 如果解码失败，可能是旧格式的数据，直接返回原始字符串
            log.debug("Base64解码失败，可能是旧格式数据: {}", encodedText);
            return encodedText;
        }
    }

    /**
     * 清理直播间消息缓存（用于修复emoji乱码问题）
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     */
    public void cleanLiveRoomMessageCache(String roomId, Integer appId) {
        try {
            String redisKey = String.format("liveroom:%d:%s:messages", appId, roomId);

            // 删除现有的乱码缓存
            redisZSetService.delete(redisKey);

            log.info("已清理直播间消息缓存: roomId={}, appId={}", roomId, appId);
        } catch (Exception e) {
            log.error("清理直播间消息缓存失败: roomId={}, appId={}", roomId, appId, e);
        }
    }
}