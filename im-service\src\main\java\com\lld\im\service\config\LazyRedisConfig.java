package com.lld.im.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.redisson.api.RedissonClient;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.redisson.Redisson;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 使用@Lazy延迟初始化的Redis配置类
 * 解决Spring Boot配置文件加载时序问题
 * 支持多环境：dev(单机)、cluster(集群测试)、prod(生产集群)
 */
@Slf4j
@Configuration
public class LazyRedisConfig {

    // Redis 基本配置
    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.database:0}")
    private int redisDatabase;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.redis.timeout:3000}")
    private int timeout;

    // Redis 集群配置
    @Value("${spring.redis.cluster.nodes:}")
    private String clusterNodes;

    @Value("${spring.redis.redisson.enable-nat-map:false}")
    private boolean enableNatMap;

    @Value("${spring.redis.redisson.nat-map:}")
    private String natMapConfig;

    /**
     * 延迟初始化的RedissonClient
     * @Lazy注解确保只有在实际使用时才创建Bean，此时配置文件已完全加载
     * 自动检测单机/集群模式
     */
    @Bean
    @Lazy
    @Primary
    public RedissonClient lazyRedissonClient() {
        log.info("=== 开始创建Redis连接 ===");

        // 验证配置读取情况
        log.info("Redis主机: {}:{}", redisHost, redisPort);
        log.info("集群节点: {}", clusterNodes);
        log.info("密码配置: {}", (redisPassword != null && !redisPassword.trim().isEmpty()) ? "已配置" : "未配置");
        log.info("连接超时: {}ms", timeout);

        Config config = new Config();

        // 判断是否为集群模式
        boolean isClusterMode = clusterNodes != null && !clusterNodes.trim().isEmpty();

        if (isClusterMode) {
            log.info("使用Redis集群模式");
            return createClusterRedissonClient(config);
        } else {
            log.info("使用Redis单机模式");
            return createSingleRedissonClient(config);
        }
    }

    /**
     * 创建集群模式的RedissonClient
     */
    private RedissonClient createClusterRedissonClient(Config config) {
        String[] nodeArray = clusterNodes.split(",");

        // 配置集群服务器
        org.redisson.config.ClusterServersConfig clusterConfig = config.useClusterServers();

        // 添加集群节点地址
        for (String node : nodeArray) {
            String nodeAddress = node.trim();
            if (!nodeAddress.startsWith("redis://")) {
                nodeAddress = "redis://" + nodeAddress;
            }
            clusterConfig.addNodeAddress(nodeAddress);
        }

        // 基本配置 - 从配置文件读取密码
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            clusterConfig.setPassword(redisPassword);
            log.info("Redis密码: 已配置");
        } else {
            log.warn("Redis密码: 未配置，使用无密码连接");
        }

        clusterConfig.setScanInterval(0)  // 禁用集群拓扑扫描
                .setTimeout(timeout)
                .setConnectTimeout(10000)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setMasterConnectionPoolSize(200)
                .setSlaveConnectionPoolSize(200)
                .setMasterConnectionMinimumIdleSize(20)
                .setSlaveConnectionMinimumIdleSize(20)
                .setSubscriptionConnectionPoolSize(50)
                .setSubscriptionConnectionMinimumIdleSize(1)
                .setReadMode(org.redisson.config.ReadMode.SLAVE)
                .setSubscriptionMode(org.redisson.config.SubscriptionMode.SLAVE)
                .setLoadBalancer(new org.redisson.connection.balancer.RoundRobinLoadBalancer())
                // 优化PING和连接管理参数
                .setPingConnectionInterval(60000)    // 增加PING间隔到60秒
                .setIdleConnectionTimeout(120000)    // 空闲连接超时2分钟
                .setKeepAlive(true)                  // 启用TCP keepalive
                .setTcpNoDelay(true);                // 禁用Nagle算法，减少延迟

        // 配置 NAT 映射
        if (enableNatMap && natMapConfig != null && !natMapConfig.trim().isEmpty()) {
            java.util.Map<String, String> natMap = parseNatMapConfig(natMapConfig);
            if (!natMap.isEmpty()) {
                clusterConfig.setNatMap(natMap);
                log.info("Redis集群 NAT 映射已启用，映射数量: {}", natMap.size());
            }
        }

        // 设置编码器 - 明确指定UTF-8编码以支持emoji表情
        org.redisson.client.codec.StringCodec stringCodec = new org.redisson.client.codec.StringCodec(java.nio.charset.StandardCharsets.UTF_8);
        config.setCodec(stringCodec);

        log.info("Redis集群配置完成 - 节点数: {}, NAT映射: {}",
                nodeArray.length, enableNatMap ? "启用" : "禁用");

        RedissonClient client = Redisson.create(config);
        log.info("✅ Redis集群连接创建成功");
        return client;
    }

    /**
     * 创建单机模式的RedissonClient
     */
    private RedissonClient createSingleRedissonClient(Config config) {
        // 配置单机服务器
        org.redisson.config.SingleServerConfig singleConfig = config.useSingleServer();

        // 设置服务器地址
        String serverAddress = "redis://" + redisHost + ":" + redisPort;
        singleConfig.setAddress(serverAddress);

        // 设置数据库
        singleConfig.setDatabase(redisDatabase);

        // 基本配置 - 从配置文件读取密码
        if (redisPassword != null && !redisPassword.trim().isEmpty()) {
            singleConfig.setPassword(redisPassword);
            log.info("Redis密码: 已配置");
        } else {
            log.info("Redis密码: 未配置，使用无密码连接");
        }

        singleConfig.setTimeout(timeout)
                .setConnectTimeout(5000)
                .setRetryAttempts(3)
                .setRetryInterval(1000)
                .setConnectionPoolSize(50)
                .setConnectionMinimumIdleSize(5)
                .setSubscriptionConnectionPoolSize(25)
                .setSubscriptionConnectionMinimumIdleSize(1)
                // 优化PING和连接管理参数
                .setPingConnectionInterval(60000)    // 增加PING间隔到60秒
                .setIdleConnectionTimeout(120000)    // 空闲连接超时2分钟
                .setKeepAlive(true)                  // 启用TCP keepalive
                .setTcpNoDelay(true);                // 禁用Nagle算法，减少延迟

        // 设置编码器 - 明确指定UTF-8编码以支持emoji表情
        org.redisson.client.codec.StringCodec stringCodec = new org.redisson.client.codec.StringCodec(java.nio.charset.StandardCharsets.UTF_8);
        config.setCodec(stringCodec);

        log.info("Redis单机配置完成 - 地址: {}, 数据库: {}", serverAddress, redisDatabase);

        RedissonClient client = Redisson.create(config);
        log.info("✅ Redis单机连接创建成功");
        return client;
    }

    /**
     * 延迟初始化的RedisConnectionFactory
     */
    @Bean
    @Lazy
    @Primary
    public RedisConnectionFactory lazyRedisConnectionFactory() {
        return new RedissonConnectionFactory(lazyRedissonClient());
    }

    /**
     * 延迟初始化的RedisTemplate
     * 注意：禁用ZSet操作以避免Redisson兼容性问题
     */
    @Bean("redisTemplate")
    @Lazy
    @Primary
    public RedisTemplate<Object, Object> lazyRedisTemplate() {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(lazyRedisConnectionFactory());

        // 配置序列化器
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());

        template.afterPropertiesSet();

        log.warn("⚠️ RedisTemplate已创建，但建议ZSet操作使用Redisson原生API以避免兼容性问题");
        return template;
    }

    /**
     * 延迟初始化的StringRedisTemplate
     * 专门用于String类型的Redis操作，与Redisson客户端兼容
     * 注意：不使用@Primary，避免与RedisTemplate产生冲突
     */
    @Bean("stringRedisTemplate")
    @Lazy
    public StringRedisTemplate lazyStringRedisTemplate() {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(lazyRedisConnectionFactory());

        // StringRedisTemplate默认使用StringRedisSerializer，无需额外配置
        template.afterPropertiesSet();

        log.info("StringRedisTemplate创建成功，使用Redisson连接工厂");
        return template;
    }

    /**
     * 解析 NAT 映射配置
     */
    private java.util.Map<String, String> parseNatMapConfig(String natMapConfig) {
        java.util.Map<String, String> natMap = new java.util.HashMap<>();
        String[] mappings = natMapConfig.split(",");

        for (String mapping : mappings) {
            String[] parts = mapping.trim().split("=");
            if (parts.length == 2) {
                natMap.put(parts[0].trim(), parts[1].trim());
            }
        }

        return natMap;
    }
}
