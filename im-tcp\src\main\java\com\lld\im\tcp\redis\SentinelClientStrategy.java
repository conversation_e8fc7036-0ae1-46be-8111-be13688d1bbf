package com.lld.im.tcp.redis;

import com.lld.im.codec.config.BootstrapConfig;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.Config;
import org.redisson.config.SentinelServersConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Redis哨兵模式连接策略
 * 
 * @author: lld
 * @version: 1.0
 */
public class SentinelClientStrategy implements RedisClientStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(SentinelClientStrategy.class);

    @Override
    public RedissonClient getRedissonClient(BootstrapConfig.RedisConfig redisConfig) {
        logger.info("初始化Redis哨兵连接...");
        
        Config config = new Config();
        SentinelServersConfig sentinelConfig = config.useSentinelServers();
        
        // 设置主节点名称
        if (redisConfig.getSentinel() != null && StringUtils.isNotBlank(redisConfig.getSentinel().getMasterName())) {
            sentinelConfig.setMasterName(redisConfig.getSentinel().getMasterName());
            logger.info("设置Redis主节点名称: {}", redisConfig.getSentinel().getMasterName());
        } else {
            throw new IllegalArgumentException("Redis哨兵模式下必须配置masterName");
        }
        
        // 添加哨兵节点
        if (redisConfig.getSentinel().getSentinels() != null) {
            for (String sentinel : redisConfig.getSentinel().getSentinels()) {
                String sentinelAddress = sentinel.startsWith("redis://") ? sentinel : "redis://" + sentinel;
                sentinelConfig.addSentinelAddress(sentinelAddress);
                logger.info("添加Redis哨兵节点: {}", sentinelAddress);
            }
        } else {
            throw new IllegalArgumentException("Redis哨兵模式下必须配置sentinels节点列表");
        }
        
        // 设置密码
        if (StringUtils.isNotBlank(redisConfig.getPassword())) {
            sentinelConfig.setPassword(redisConfig.getPassword());
        }
        
        // 设置数据库
        sentinelConfig.setDatabase(redisConfig.getDatabase());
        
        // 设置基本参数
        sentinelConfig.setTimeout(redisConfig.getTimeout())
                     .setMasterConnectionPoolSize(redisConfig.getPoolSize())
                     .setSlaveConnectionPoolSize(redisConfig.getPoolSize())
                     .setMasterConnectionMinimumIdleSize(redisConfig.getPoolMinIdle())
                     .setSlaveConnectionMinimumIdleSize(redisConfig.getPoolMinIdle())
                     .setConnectTimeout(redisConfig.getPoolConnTimeout());
        
        // 设置哨兵特有参数
        // 注意：Redisson 3.15.6版本的SentinelServersConfig中故障转移相关参数使用默认值
        // failoverTimeout配置项在此版本中没有直接对应的setter方法
        
        // 设置编码器 - 明确指定UTF-8编码以支持emoji表情
        StringCodec stringCodec = new StringCodec(java.nio.charset.StandardCharsets.UTF_8);
        config.setCodec(stringCodec);
        
        logger.info("Redis哨兵连接配置完成，主节点: {}, 哨兵数量: {}", 
                   redisConfig.getSentinel().getMasterName(), 
                   redisConfig.getSentinel().getSentinels().size());
        return Redisson.create(config);
    }
    
    @Override
    public String getStrategyName() {
        return "sentinel";
    }
}
